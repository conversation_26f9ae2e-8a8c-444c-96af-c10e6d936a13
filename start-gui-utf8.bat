@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   GraalVM 元数据管理工具 - UTF-8版本
echo ========================================
echo.

echo [1/3] 设置UTF-8编码环境...
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8

echo [2/3] 检查并编译...
if not exist "src\main\java\tools\MetadataChecker.class" (
    echo 正在编译...
    javac -encoding UTF-8 -cp . src/main/java/tools/MetadataChecker.java
    
    if %errorlevel% neq 0 (
        echo ❌ 编译失败
        pause
        exit /b 1
    )
    echo ✅ 编译成功
) else (
    echo ✅ 已编译
)

echo.
echo [3/3] 启动GUI工具...
echo.
echo 🚀 正在启动元数据管理工具...
echo 💡 已设置UTF-8编码支持

java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -cp src/main/java tools.MetadataChecker

echo.
echo 工具已关闭
pause
