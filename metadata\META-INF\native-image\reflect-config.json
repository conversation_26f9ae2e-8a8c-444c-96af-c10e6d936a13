[{"name": "[B"}, {"name": "[C"}, {"name": "[D"}, {"name": "[F"}, {"name": "[I"}, {"name": "[J"}, {"name": "[Ljava.awt.event.MouseMotionListener;"}, {"name": "[Ljava.lang.String;"}, {"name": "[Ljavax.management.openmbean.CompositeData;"}, {"name": "[Lsun.security.pkcs.SignerInfo;"}, {"name": "[S"}, {"name": "[Z"}, {"name": "auth<PERSON><PERSON><PERSON><PERSON>", "methods": [{"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "auth.netty.AuthClient$1"}, {"name": "auth.netty.PacketDecoder"}, {"name": "auth.netty.PacketEncoder"}, {"name": "ch.qos.logback.classic.encoder.PatternLayoutEncoder", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.joran.SerializedModelConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.DateConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LevelConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LineSeparatorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LoggerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.MessageConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.MethodOfCallerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.ThreadConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.util.DefaultJoranConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.Console<PERSON>ppender", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.FileAppender", "methods": [{"name": "setPrudent", "parameterTypes": ["boolean"]}, {"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.OutputStreamAppender", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["ch.qos.logback.core.encoder.Encoder"]}]}, {"name": "ch.qos.logback.core.encoder.Encoder", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.encoder.LayoutWrappingEncoder", "methods": [{"name": "setParent", "parameterTypes": ["ch.qos.logback.core.spi.ContextAware"]}]}, {"name": "ch.qos.logback.core.pattern.PatternLayoutEncoderBase", "methods": [{"name": "setPattern", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.pattern.color.RedCompositeConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.pattern.color.YellowCompositeConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.rolling.RollingFileAppender", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setFile", "parameterTypes": ["java.lang.String"]}, {"name": "setRollingPolicy", "parameterTypes": ["ch.qos.logback.core.rolling.RollingPolicy"]}]}, {"name": "ch.qos.logback.core.rolling.RollingPolicy", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.rolling.RollingPolicyBase", "methods": [{"name": "setFileNamePattern", "parameterTypes": ["java.lang.String"]}, {"name": "setParent", "parameterTypes": ["ch.qos.logback.core.FileAppender"]}]}, {"name": "ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy", "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMaxFileSize", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"name": "ch.qos.logback.core.rolling.TimeBasedRollingPolicy", "methods": [{"name": "setMaxHistory", "parameterTypes": ["int"]}, {"name": "setTotalSizeCap", "parameterTypes": ["ch.qos.logback.core.util.FileSize"]}]}, {"name": "ch.qos.logback.core.rolling.helper.DateTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.rolling.helper.IntegerTokenConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.core.spi.ContextAware", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "ch.qos.logback.core.util.FileSize", "methods": [{"name": "valueOf", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.alibaba.druid.mock.MockDriver", "queryAllPublicConstructors": true}, {"name": "com.alibaba.druid.mock.MockDriverMBean", "queryAllPublicMethods": true}, {"name": "com.alibaba.druid.proxy.DruidDriver", "queryAllPublicConstructors": true}, {"name": "com.alibaba.druid.proxy.DruidDriverMBean", "queryAllPublicMethods": true}, {"name": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.intellij.openapi.editor.impl.EditorCopyPasteHelperImpl$CopyPasteOptionsTransferableData"}, {"name": "com.intellij.rt.execution.application.AppMainV2$Agent", "methods": [{"name": "premain", "parameterTypes": ["java.lang.String", "java.lang.instrument.Instrumentation"]}]}, {"name": "com.mysql.fabric.jdbc.FabricMySQLDriver"}, {"name": "com.mysql.jdbc.AbandonedConnectionCleanupThread"}, {"name": "com.mysql.jdbc.ConnectionPropertiesImpl", "allDeclaredFields": true}, {"name": "com.mysql.jdbc.Driver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.mysql.jdbc.JDBC42PreparedStatement", "methods": [{"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "java.lang.String"]}, {"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "java.lang.String", "java.lang.String"]}, {"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "java.lang.String", "java.lang.String", "com.mysql.jdbc.PreparedStatement$ParseInfo"]}]}, {"name": "com.mysql.jdbc.JDBC42ResultSet", "methods": [{"name": "<init>", "parameterTypes": ["long", "long", "com.mysql.jdbc.MySQLConnection", "com.mysql.jdbc.StatementImpl"]}, {"name": "<init>", "parameterTypes": ["java.lang.String", "com.mysql.jdbc.Field[]", "com.mysql.jdbc.RowData", "com.mysql.jdbc.MySQLConnection", "com.mysql.jdbc.StatementImpl"]}]}, {"name": "com.mysql.jdbc.JDBC42UpdatableResultSet", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "com.mysql.jdbc.Field[]", "com.mysql.jdbc.RowData", "com.mysql.jdbc.MySQLConnection", "com.mysql.jdbc.StatementImpl"]}]}, {"name": "com.mysql.jdbc.JDBC4Connection", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "int", "java.util.Properties", "java.lang.String", "java.lang.String"]}]}, {"name": "com.mysql.jdbc.JDBC4DatabaseMetaData", "methods": [{"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "java.lang.String"]}]}, {"name": "com.mysql.jdbc.JDBC4DatabaseMetaDataUsingInfoSchema", "methods": [{"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "java.lang.String"]}]}, {"name": "com.mysql.jdbc.StandardSocketFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.mysql.jdbc.exceptions.jdbc4.CommunicationsException", "methods": [{"name": "<init>", "parameterTypes": ["com.mysql.jdbc.MySQLConnection", "long", "long", "java.lang.Exception"]}]}, {"name": "com.mysql.jdbc.exceptions.jdbc4.MySQLNonTransientConnectionException", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "int"]}]}, {"name": "com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "int"]}]}, {"name": "com.mysql.jdbc.log.StandardLogger", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "com.sun.crypto.provider.AESCipher$General", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ARCFOURCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.ChaCha20Cipher$ChaCha20Poly1305", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DESedeCipher", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.DHParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.GaloisCounterMode$AESGCM", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.HmacCore$HmacSHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.HmacCore$HmacSHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.HmacPKCS12PBECore$HmacPKCS12PBE_SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.PBEKeyFactory$PBEWithMD5AndDES", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.PBES2Core$HmacSHA256AndAES_256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.PBES2Parameters$General", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.PBES2Parameters$HmacSHA256AndAES_256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.crypto.provider.TlsMasterSecretGenerator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.management.GarbageCollectorMXBean", "queryAllPublicMethods": true}, {"name": "com.sun.management.GcInfo", "queryAllPublicMethods": true}, {"name": "com.sun.management.HotSpotDiagnosticMXBean", "queryAllPublicMethods": true}, {"name": "com.sun.management.OperatingSystemMXBean", "queryAllPublicMethods": true}, {"name": "com.sun.management.ThreadMXBean", "queryAllPublicMethods": true}, {"name": "com.sun.management.VMOption", "queryAllPublicMethods": true}, {"name": "com.sun.management.internal.GarbageCollectorExtImpl", "queryAllPublicConstructors": true}, {"name": "com.sun.management.internal.HotSpotDiagnostic", "queryAllPublicConstructors": true}, {"name": "com.sun.management.internal.HotSpotThreadImpl", "queryAllPublicConstructors": true}, {"name": "com.sun.management.internal.OperatingSystemImpl", "queryAllPublicConstructors": true}, {"name": "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "configs.ClonePlayer", "allDeclaredFields": true}, {"name": "configs.CubeConfig", "allDeclaredFields": true}, {"name": "configs.EnhanceConfig", "allDeclaredFields": true}, {"name": "configs.GoldenConfig", "allDeclaredFields": true}, {"name": "configs.MonsterCollectionConfig", "allDeclaredFields": true}, {"name": "configs.Mu<PERSON>RoomReward", "allDeclaredFields": true}, {"name": "configs.MysqlConfig", "allDeclaredFields": true}, {"name": "configs.NFlameConfig", "allDeclaredFields": true}, {"name": "configs.NebuliteConfig", "allDeclaredFields": true}, {"name": "configs.PacketConfig", "allDeclaredFields": true}, {"name": "configs.QuestConfig", "allDeclaredFields": true}, {"name": "configs.SealedConfig", "allDeclaredFields": true}, {"name": "configs.ServerConfig", "allDeclaredFields": true}, {"name": "handling.MapleServerHandler", "methods": [{"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "userEventTriggered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "handling.netty.MaplePacketDecoder"}, {"name": "handling.netty.MaplePacketEncoder"}, {"name": "handling.netty.ServerInitializer"}, {"name": "io.netty.bootstrap.ServerBootstrap$1"}, {"name": "io.netty.bootstrap.ServerBootstrap$ServerBootstrapAcceptor", "methods": [{"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}]}, {"name": "io.netty.buffer.AbstractByteBufAllocator", "queryAllDeclaredMethods": true}, {"name": "io.netty.buffer.AbstractReferenceCountedByteBuf", "fields": [{"name": "refCnt"}]}, {"name": "io.netty.channel.AbstractChannelHandlerContext", "fields": [{"name": "handlerState"}]}, {"name": "io.netty.channel.ChannelDuplexHandler", "methods": [{"name": "bind", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "close", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "connect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "deregister", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "disconnect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "flush", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "read", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}]}, {"name": "io.netty.channel.ChannelHandlerAdapter", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}]}, {"name": "io.netty.channel.ChannelInboundHandlerAdapter", "methods": [{"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "userEventTriggered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "io.netty.channel.ChannelInitializer", "methods": [{"name": "channelRegistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}]}, {"name": "io.netty.channel.ChannelOutboundBuffer", "fields": [{"name": "totalPendingSize"}, {"name": "unwritable"}]}, {"name": "io.netty.channel.ChannelOutboundHandlerAdapter", "methods": [{"name": "bind", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "close", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "connect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "deregister", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "disconnect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "flush", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "read", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}]}, {"name": "io.netty.channel.DefaultChannelConfig", "fields": [{"name": "autoRead"}, {"name": "writeBufferWaterMark"}]}, {"name": "io.netty.channel.DefaultChannelPipeline", "fields": [{"name": "estimator<PERSON><PERSON><PERSON>"}]}, {"name": "io.netty.channel.DefaultChannelPipeline$HeadContext", "methods": [{"name": "bind", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "close", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "connect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "java.net.SocketAddress", "io.netty.channel.ChannelPromise"]}, {"name": "deregister", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "disconnect", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "io.netty.channel.ChannelPromise"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "flush", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "read", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "userEventTriggered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "write", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object", "io.netty.channel.ChannelPromise"]}]}, {"name": "io.netty.channel.DefaultChannelPipeline$TailContext", "methods": [{"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "userEventTriggered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "io.netty.channel.socket.nio.NioServerSocketChannel", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "io.netty.channel.socket.nio.NioSocketChannel", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "io.netty.handler.codec.ByteToMessageDecoder", "methods": [{"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "userEventTriggered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "io.netty.handler.codec.MessageToByteEncoder", "methods": [{"name": "write", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object", "io.netty.channel.ChannelPromise"]}]}, {"name": "io.netty.handler.timeout.IdleStateHandler", "methods": [{"name": "channelActive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.netty.channel.ChannelHandlerContext"]}, {"name": "write", "parameterTypes": ["io.netty.channel.ChannelHandlerContext", "java.lang.Object", "io.netty.channel.ChannelPromise"]}]}, {"name": "io.netty.internal.tcnative.SSLContext"}, {"name": "io.netty.util.DefaultAttributeMap", "fields": [{"name": "attributes"}]}, {"name": "io.netty.util.DefaultAttributeMap$DefaultAttribute", "fields": [{"name": "attributeMap"}]}, {"name": "io.netty.util.Recycler$DefaultHandle", "fields": [{"name": "state"}]}, {"name": "io.netty.util.ReferenceCountUtil", "queryAllDeclaredMethods": true}, {"name": "io.netty.util.ResourceLeakDetector$DefaultResourceLeak", "fields": [{"name": "droppedRecords"}, {"name": "head"}]}, {"name": "io.netty.util.concurrent.DefaultPromise", "fields": [{"name": "result"}]}, {"name": "io.netty.util.concurrent.SingleThreadEventExecutor", "fields": [{"name": "state"}, {"name": "threadProperties"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueColdProducerFields", "fields": [{"name": "producerLimit"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueConsumerFields", "fields": [{"name": "consumerIndex"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueProducerFields", "fields": [{"name": "producerIndex"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueConsumerIndexField", "fields": [{"name": "consumerIndex"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueProducerIndexField", "fields": [{"name": "producerIndex"}]}, {"name": "io.netty.util.internal.shaded.org.jctools.queues.MpscArrayQueueProducerLimitField", "fields": [{"name": "producerLimit"}]}, {"name": "java.awt.Image"}, {"name": "java.awt.Point", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.awt.SequencedEvent"}, {"name": "java.awt.event.KeyEvent", "fields": [{"name": "VK_A"}, {"name": "VK_BACK_SLASH"}, {"name": "VK_BACK_SPACE"}, {"name": "VK_C"}, {"name": "VK_CONTEXT_MENU"}, {"name": "VK_COPY"}, {"name": "VK_CUT"}, {"name": "VK_DELETE"}, {"name": "VK_DOWN"}, {"name": "VK_END"}, {"name": "VK_ENTER"}, {"name": "VK_ESCAPE"}, {"name": "VK_F10"}, {"name": "VK_H"}, {"name": "VK_HOME"}, {"name": "VK_INSERT"}, {"name": "VK_KP_DOWN"}, {"name": "VK_KP_LEFT"}, {"name": "VK_KP_RIGHT"}, {"name": "VK_KP_UP"}, {"name": "VK_LEFT"}, {"name": "VK_O"}, {"name": "VK_PAGE_DOWN"}, {"name": "VK_PAGE_UP"}, {"name": "VK_PASTE"}, {"name": "VK_RIGHT"}, {"name": "VK_SLASH"}, {"name": "VK_SPACE"}, {"name": "VK_T"}, {"name": "VK_TAB"}, {"name": "VK_UP"}, {"name": "VK_V"}, {"name": "VK_X"}]}, {"name": "java.io.FilePermission"}, {"name": "java.io.InputStream"}, {"name": "java.io.Reader"}, {"name": "java.lang.Bo<PERSON>an", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Byte", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Character", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Deprecated", "queryAllPublicMethods": true}, {"name": "java.lang.Double", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Enum", "methods": [{"name": "name", "parameterTypes": []}]}, {"name": "java.lang.Float", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Integer", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.Long", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.ProcessHandle", "methods": [{"name": "current", "parameterTypes": []}, {"name": "pid", "parameterTypes": []}]}, {"name": "java.lang.RuntimePermission"}, {"name": "java.lang.Short", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.StackTraceElement", "queryAllPublicMethods": true}, {"name": "java.lang.String", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.System", "methods": [{"name": "nanoTime", "parameterTypes": []}]}, {"name": "java.lang.Thread", "fields": [{"name": "threadLocalRandomProbe"}], "methods": [{"name": "ofVirtual", "parameterTypes": []}]}, {"name": "java.lang.ThreadBuilders$VirtualThreadBuilder", "methods": [{"name": "factory", "parameterTypes": []}]}, {"name": "java.lang.Void", "fields": [{"name": "TYPE"}]}, {"name": "java.lang.management.BufferPoolMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.ClassLoadingMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.CompilationMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.LockInfo", "queryAllPublicMethods": true}, {"name": "java.lang.management.ManagementFactory", "methods": [{"name": "getRuntimeMXBean", "parameterTypes": []}]}, {"name": "java.lang.management.ManagementPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.management.MemoryMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.MemoryManagerMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.MemoryPoolMXBean", "queryAllPublicMethods": true}, {"name": "java.lang.management.MemoryUsage", "queryAllPublicMethods": true}, {"name": "java.lang.management.MonitorInfo", "queryAllPublicMethods": true}, {"name": "java.lang.management.PlatformLoggingMXBean", "queryAllPublicMethods": true, "methods": [{"name": "getLoggerLevel", "parameterTypes": ["java.lang.String"]}, {"name": "getLoggerNames", "parameterTypes": []}, {"name": "getParentLoggerName", "parameterTypes": ["java.lang.String"]}, {"name": "setLoggerLevel", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.lang.management.RuntimeMXBean", "queryAllPublicMethods": true, "methods": [{"name": "getInputArguments", "parameterTypes": []}]}, {"name": "java.lang.management.ThreadInfo", "queryAllPublicMethods": true}, {"name": "java.math.BigDecimal", "methods": [{"name": "toPlainString", "parameterTypes": []}]}, {"name": "java.math.BigInteger"}, {"name": "java.net.NetPermission"}, {"name": "java.net.SocketPermission"}, {"name": "java.net.URL"}, {"name": "java.net.URLPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.nio.Bits", "fields": [{"name": "MAX_MEMORY"}, {"name": "UNALIGNED"}]}, {"name": "java.nio.Buffer", "fields": [{"name": "address"}]}, {"name": "java.nio.ByteBuffer", "methods": [{"name": "alignedSlice", "parameterTypes": ["int"]}]}, {"name": "java.nio.CharBuffer"}, {"name": "java.nio.DirectByteBuffer", "methods": [{"name": "<init>", "parameterTypes": ["long", "int"]}, {"name": "<init>", "parameterTypes": ["long", "long"]}]}, {"name": "java.nio.channels.spi.SelectorProvider", "methods": [{"name": "openServerSocketChannel", "parameterTypes": ["java.net.ProtocolFamily"]}, {"name": "openSocketChannel", "parameterTypes": ["java.net.ProtocolFamily"]}]}, {"name": "java.rmi.MarshalledObject", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Object"]}, {"name": "get", "parameterTypes": []}]}, {"name": "java.rmi.Remote"}, {"name": "java.security.AlgorithmParametersSpi"}, {"name": "java.security.AllPermission"}, {"name": "java.security.KeyStoreSpi"}, {"name": "java.security.MessageDigestSpi"}, {"name": "java.security.SecureRandomParameters"}, {"name": "java.security.SecurityPermission"}, {"name": "java.security.cert.PKIXRevocationChecker"}, {"name": "java.security.interfaces.DSAPrivateKey"}, {"name": "java.security.interfaces.DSAPublicKey"}, {"name": "java.security.interfaces.RSAPrivateKey"}, {"name": "java.security.interfaces.RSAPublicKey"}, {"name": "java.security.spec.DSAParameterSpec"}, {"name": "java.sql.Blob", "methods": [{"name": "truncate", "parameterTypes": ["long"]}]}, {"name": "java.sql.JDBCType"}, {"name": "java.sql.NClob"}, {"name": "java.sql.SQLException", "fields": [{"name": "next"}]}, {"name": "java.util.ArrayList", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.Calendar", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.Collection", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.Date"}, {"name": "java.util.Gregorian<PERSON>r", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.HashMap", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.LinkedHashMap", "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true}, {"name": "java.util.LinkedList", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.List"}, {"name": "java.util.Locale", "allDeclaredClasses": true, "methods": [{"name": "getDefault", "parameterTypes": ["java.util.Locale$Category"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.util.Locale$Category", "java.util.Locale"]}]}, {"name": "java.util.Locale$Category"}, {"name": "java.util.PropertyPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.util.Random", "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredConstructors": true, "allPublicConstructors": true}, {"name": "java.util.concurrent.Executors", "methods": [{"name": "newVirtualThreadPerTaskExecutor", "parameterTypes": []}]}, {"name": "java.util.concurrent.ForkJoinTask", "fields": [{"name": "aux"}, {"name": "status"}]}, {"name": "java.util.concurrent.atomic.AtomicBoolean", "fields": [{"name": "value"}]}, {"name": "java.util.concurrent.atomic.AtomicReference", "fields": [{"name": "value"}]}, {"name": "java.util.concurrent.atomic.Striped64", "fields": [{"name": "base"}, {"name": "cellsBusy"}]}, {"name": "java.util.logging.LogManager", "methods": [{"name": "getLoggingMXBean", "parameterTypes": []}]}, {"name": "java.util.logging.LoggingMXBean", "queryAllPublicMethods": true}, {"name": "javax.management.MBeanOperationInfo", "queryAllPublicMethods": true, "methods": [{"name": "getSignature", "parameterTypes": []}]}, {"name": "javax.management.MBeanServerBuilder", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "javax.management.ObjectName"}, {"name": "javax.management.StandardEmitterMBean", "methods": [{"name": "cacheMBeanInfo", "parameterTypes": ["javax.management.MBeanInfo"]}, {"name": "getCachedMBeanInfo", "parameterTypes": []}, {"name": "getMBeanInfo", "parameterTypes": []}]}, {"name": "javax.management.openmbean.CompositeData"}, {"name": "javax.management.openmbean.OpenMBeanOperationInfoSupport"}, {"name": "javax.management.openmbean.TabularData"}, {"name": "javax.security.auth.x500.X500Principal", "fields": [{"name": "thisX500Name"}], "methods": [{"name": "<init>", "parameterTypes": ["sun.security.x509.X500Name"]}]}, {"name": "javax.smartcardio.CardPermission"}, {"name": "javax.swing.plaf.basic.BasicListUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicMenuItemUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicOptionPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicPanelUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicPopupMenuUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}, {"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicRootPaneUI", "methods": [{"name": "loadActionMap", "parameterTypes": ["javax.swing.plaf.basic.LazyActionMap"]}]}, {"name": "javax.swing.plaf.basic.BasicTextAreaUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.basic.BasicViewportUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalButtonUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalComboBoxUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalLabelUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalPopupMenuSeparatorUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalRootPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalScrollBarUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalScrollPaneUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "javax.swing.plaf.metal.MetalTextFieldUI", "methods": [{"name": "createUI", "parameterTypes": ["javax.swing.JComponent"]}]}, {"name": "jdk.internal.misc.Unsafe", "methods": [{"name": "getUnsafe", "parameterTypes": []}]}, {"name": "jdk.management.jfr.ConfigurationInfo", "queryAllPublicMethods": true}, {"name": "jdk.management.jfr.EventTypeInfo", "queryAllPublicMethods": true}, {"name": "jdk.management.jfr.FlightRecorderMXBean", "queryAllPublicMethods": true}, {"name": "jdk.management.jfr.FlightRecorderMXBeanImpl", "queryAllPublicConstructors": true, "methods": [{"name": "cacheMBeanInfo", "parameterTypes": ["javax.management.MBeanInfo"]}, {"name": "getCachedMBeanInfo", "parameterTypes": []}, {"name": "getMBeanInfo", "parameterTypes": []}, {"name": "getNotificationInfo", "parameterTypes": []}]}, {"name": "jdk.management.jfr.RecordingInfo", "queryAllPublicMethods": true}, {"name": "jdk.management.jfr.SettingDescriptorInfo", "queryAllPublicMethods": true}, {"name": "org.bouncycastle.jce.provider.BouncyCastleProvider"}, {"name": "org.bouncycastle.openssl.PEMParser"}, {"name": "org.graalvm.polyglot.io.IOHelper", "fields": [{"name": "ACCESS"}]}, {"name": "org.graalvm.polyglot.management.Management", "fields": [{"name": "ACCESS"}]}, {"name": "server.ShutdownServer", "queryAllPublicConstructors": true}, {"name": "server.ShutdownServerMBean", "queryAllPublicMethods": true}, {"name": "server.commands.AdminCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$DCAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$DropRate", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$DropgRate", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$ExpRate", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$ItemSize", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$MesoRate", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$ResetChannel", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$Rev", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$SetChannelStatus", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$ShowTrace", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$StartProfiling", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$StopProfiling", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TMegaphone", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestBuffTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestCloneTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestEtcTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestEventTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestMapTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$TestWorldTimer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$Threads", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$ToggleOffense", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$UnBan", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$UnbanIP", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$calculatedistance", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$clearbanip", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$cthread", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$dbexp", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$resetskill", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$sothread", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$testPacket", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.AdminCommand$testdistance", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.DonatorCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.GMCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Ban", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$BanDrop", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Disease", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Fame", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$GMMessage", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$GetFamiliarItem", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$GetItem", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$GetSkill", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Job", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$KillAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$KillAllDrops", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$KillMap", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$KillMonsterByOID", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Level", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$LevelUp", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$MtsAllSellItems", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$MtsSellItems", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Respawn", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$SP", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$SaveAuction", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$Shop", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$WarpHere", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$WarpHereAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$delitem", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$gainExp", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$lockitem", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$removeHunterDoor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$spawnHunterDoor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$spawnRune", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.GMCommand$unregeim", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$BGM", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Clock", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$DC", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$FakeRelog", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Find", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Heal", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$HealMap", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$ItemCheck", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Kill", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$LowHP", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$MapInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$MtsDoneItems", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$RemoveDrops", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Say", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$ShowMyIP", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$SkillLvlInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$SpawnInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$Suicide", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$TempBan", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$WhosThere", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$channelonline", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$charInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$checkcopy", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$dbInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$eimInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$emInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$eqpSlot", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$gameInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$god", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$hide", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$liedetector", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$mapID", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$myPos", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$npcInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$online", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$portalInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$reactorInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$warp", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.InternCommand$warpxy", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.PlayerCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.PlayerCommand$EA", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.PlayerCommand$Help", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.PlayerCommand$Mob", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.PlayerCommand$Save", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperDonatorCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand", "allDeclaredClasses": true, "methods": [{"name": "getPlayerLevelRequired", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$BuffSkill", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$CleanAuction", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$DReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$DestroyPNPC", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$Drop", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$FHitReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$GetFamiliarAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$GetItemAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$Getgifts", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$GiveSkill", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$HitAll", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$HitMonster", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$HitMonsterByOID", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$HitReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$KillMonster", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$MakeMob", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$MakeNpc", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$MakeOfflineP", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$MakePNPC", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$MobBanMove", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$NPC", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$ReloadMap", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$ResetMap", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$ResetReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$SendAllNote", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$ServerMessage", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$SetReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$ShowMapLoadStatus", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$Spawn", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$SpawnElite", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$SpawnEliteMob", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$UnlockInv", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$effectnotice", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$getmeso", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$newFileJson", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$quest", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$questEx", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$questPlayer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadCollectoPcrystal", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadGMCommand", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadNFlameInfo", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadOpcode", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadRandomRewards", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadSkillCopy", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadSlideMenu", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadconfig", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$reloadhandler", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetEvent", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetEventByName", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetScriptCache", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetdrop", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetnpc", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetrank", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetshop", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$resetsign", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$seeplayer", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "server.commands.SuperGMCommand$spawnReactor", "fields": [{"name": "enabled"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.awt.Symbol", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.awt.windows.WingDings", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.java2d.marlin.DMarlinRenderingEngine", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.management.ClassLoadingImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.CompilationImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.ManagementFactoryHelper$1", "queryAllPublicConstructors": true}, {"name": "sun.management.ManagementFactoryHelper$PlatformLoggingImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.MemoryImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.MemoryManagerImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.MemoryPoolImpl", "queryAllPublicConstructors": true}, {"name": "sun.management.RuntimeImpl", "queryAllPublicConstructors": true}, {"name": "sun.misc.Unsafe", "fields": [{"name": "theUnsafe"}], "methods": [{"name": "copyMemory", "parameterTypes": ["java.lang.Object", "long", "java.lang.Object", "long", "long"]}, {"name": "getAndAddLong", "parameterTypes": ["java.lang.Object", "long", "long"]}, {"name": "getAndSetObject", "parameterTypes": ["java.lang.Object", "long", "java.lang.Object"]}, {"name": "invoke<PERSON><PERSON><PERSON>", "parameterTypes": ["java.nio.ByteBuffer"]}, {"name": "storeFence", "parameterTypes": []}]}, {"name": "sun.misc.VM"}, {"name": "sun.nio.ch.SelectorImpl", "fields": [{"name": "publicSelectedKeys"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.pkcs12.PKCS12KeyStore$DualFormatPKCS12", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DRBG", "methods": [{"name": "<init>", "parameterTypes": ["java.security.SecureRandomParameters"]}]}, {"name": "sun.security.provider.DSA$SHA224withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSA$SHA256withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSAKeyFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.DSAParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.JavaKeyStore$DualFormatJKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.JavaKeyStore$JKS", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.MD5", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA224", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA384", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA5$SHA512", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.X509Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.PSSParameters", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAKeyFactory$Legacy", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSAPSSSignature", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA224withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA256withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA384withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.KeyManagerFactoryImpl$SunX509", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.SSLContextImpl$TLSContext", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.ssl.TrustManagerFactoryImpl$PKIXFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.util.ObjectIdentifier"}, {"name": "sun.security.x509.AuthorityInfoAccessExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.AuthorityKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.BasicConstraintsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CRLDistributionPointsExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.CertificateExtensions"}, {"name": "sun.security.x509.CertificatePoliciesExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.ExtendedKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.IssuerAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.KeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.NetscapeCertTypeExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.PrivateKeyUsageExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectAlternativeNameExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "sun.security.x509.SubjectKeyIdentifierExtension", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an", "java.lang.Object"]}]}, {"name": "tools.gui.DebugUI", "methods": [{"name": "coalesceEvents", "parameterTypes": ["java.awt.AWTEvent", "java.awt.AWTEvent"]}]}, {"name": "tools.gui.ServerMonitorGUI", "methods": [{"name": "coalesceEvents", "parameterTypes": ["java.awt.AWTEvent", "java.awt.AWTEvent"]}]}]