@echo off
setlocal enabledelayedexpansion

REM GraalVM 元数据对比和完整性检查工具
REM 用途：对比新收集的元数据与resources中的元数据，检查完整性

set "NEW_METADATA_DIR=metadata\META-INF\native-image"
set "OLD_METADATA_DIR=src\main\resources\META-INF\native-image"

echo === GraalVM Metadata Compare Tool ===
echo.

REM 检查目录是否存在
if not exist "%NEW_METADATA_DIR%" (
    echo [ERROR] 新元数据目录不存在: %NEW_METADATA_DIR%
    echo 请先运行程序收集元数据
    pause
    exit /b 1
)

if not exist "%OLD_METADATA_DIR%" (
    echo [INFO] resources目录中没有元数据，这是首次收集
    set "first_time=true"
) else (
    set "first_time=false"
)

echo [INFO] 新元数据目录: %NEW_METADATA_DIR%
echo [INFO] 旧元数据目录: %OLD_METADATA_DIR%
echo.

REM 检查新元数据的完整性
echo === Check New Metadata Integrity ===
call :check_metadata_integrity "%NEW_METADATA_DIR%"
if !errorlevel! neq 0 (
    echo [WARN] 新元数据可能不完整或损坏！
    pause
)

REM 如果不是首次，进行对比
if "%first_time%"=="false" (
    echo.
    echo === Compare Metadata Differences ===
    call :compare_metadata
)

echo.
echo === Summary ===
call :show_summary

echo.
echo 是否要复制新元数据到resources目录？ (y/n):
set /p choice=
if /i "%choice%"=="y" (
    call :copy_metadata
    echo [DONE] 元数据已更新到resources目录
) else (
    echo [SKIP] 未更新resources目录
)

pause
exit /b 0

REM ==================== 函数定义 ====================

:check_metadata_integrity
set "dir=%~1"
set "issues=0"

echo 检查目录: %dir%

REM 检查必要的配置文件
set "required_files=reflect-config.json resource-config.json proxy-config.json jni-config.json serialization-config.json"

for %%f in (%required_files%) do (
    if exist "%dir%\%%f" (
        call :validate_json "%dir%\%%f"
        if !errorlevel! equ 0 (
            call :analyze_json_content "%dir%\%%f"
        ) else (
            echo   [X] %%f - JSON格式错误
            set /a issues+=1
        )
    ) else (
        echo   [?] %%f - 文件不存在（可能未使用相关功能）
    )
)

if %issues% equ 0 (
    echo [OK] 元数据完整性检查通过
) else (
    echo [WARN] 发现 %issues% 个问题
)

exit /b %issues%

:validate_json
set "file=%~1"
if not exist "%file%" exit /b 1

REM 简单的JSON格式检查
set /p first_line=<"%file%"
set "first_char=%first_line:~0,1%"

if "%first_char%"=="{" goto :check_json_end
if "%first_char%"=="[" goto :check_json_end
exit /b 1

:check_json_end
findstr /e "}" "%file%" >nul 2>&1
if %errorlevel% equ 0 exit /b 0
findstr /e "]" "%file%" >nul 2>&1
if %errorlevel% equ 0 exit /b 0
exit /b 1

:analyze_json_content
set "file=%~1"
set "filename="
for %%f in ("%file%") do set "filename=%%~nxf"

REM 获取文件大小
for %%f in ("%file%") do set "size=%%~zf"

REM 计算条目数量（简化版本）
for /f %%i in ('type "%file%" ^| find /c ":"') do set "entries=%%i"

if %size% lss 10 (
    echo   [+] %filename% - 文件很小 ^(%size% bytes, ~%entries% 条目^)
) else if %entries% lss 5 (
    echo   [+] %filename% - 条目较少 ^(%size% bytes, ~%entries% 条目^)
) else (
    echo   [+] %filename% - 正常 ^(%size% bytes, ~%entries% 条目^)
)
exit /b 0

:compare_metadata
set "differences=0"

echo 对比配置文件差异...

for %%f in (reflect-config.json resource-config.json proxy-config.json jni-config.json serialization-config.json) do (
    if exist "%NEW_METADATA_DIR%\%%f" (
        if exist "%OLD_METADATA_DIR%\%%f" (
            fc /b "%NEW_METADATA_DIR%\%%f" "%OLD_METADATA_DIR%\%%f" >nul 2>&1
            if !errorlevel! neq 0 (
                echo   [DIFF] %%f - 文件有变化
                call :show_file_diff "%%f"
                set /a differences+=1
            ) else (
                echo   [SAME] %%f - 文件相同
            )
        ) else (
            echo   [NEW] %%f - 新增文件
            set /a differences+=1
        )
    ) else (
        if exist "%OLD_METADATA_DIR%\%%f" (
            echo   [DEL] %%f - 文件已删除
            set /a differences+=1
        )
    )
)

if %differences% equ 0 (
    echo [INFO] 所有文件都相同，无需更新
) else (
    echo [INFO] 发现 %differences% 个文件有差异
)

exit /b 0

:show_file_diff
set "filename=%~1"
set "new_file=%NEW_METADATA_DIR%\%filename%"
set "old_file=%OLD_METADATA_DIR%\%filename%"

for %%f in ("%new_file%") do set "new_size=%%~zf"
for %%f in ("%old_file%") do set "old_size=%%~zf"

if %new_size% gtr %old_size% (
    set /a diff_size=%new_size%-%old_size%
    echo     文件增大了 !diff_size! bytes
) else if %new_size% lss %old_size% (
    set /a diff_size=%old_size%-%new_size%
    echo     文件减小了 !diff_size! bytes
) else (
    echo     文件大小相同但内容不同
)

exit /b 0

:show_summary
echo.
echo Metadata Statistics:

REM 统计新元数据
if exist "%NEW_METADATA_DIR%" (
    set "new_files=0"
    set "new_total_size=0"
    
    for %%f in ("%NEW_METADATA_DIR%\*.json") do (
        set /a new_files+=1
        for %%s in ("%%f") do set /a new_total_size+=%%~zs
    )
    
    echo   New metadata: !new_files! files, total size !new_total_size! bytes
)

REM 统计旧元数据
if exist "%OLD_METADATA_DIR%" (
    set "old_files=0"
    set "old_total_size=0"

    for %%f in ("%OLD_METADATA_DIR%\*.json") do (
        set /a old_files+=1
        for %%s in ("%%f") do set /a old_total_size+=%%~zs
    )

    echo   Old metadata: !old_files! files, total size !old_total_size! bytes
) else (
    echo   Old metadata: None (first time)
)

exit /b 0

:copy_metadata
echo Copying metadata...

if not exist "%OLD_METADATA_DIR%" mkdir "%OLD_METADATA_DIR%"

xcopy "%NEW_METADATA_DIR%\*" "%OLD_METADATA_DIR%\" /Y /Q >nul 2>&1

if %errorlevel% equ 0 (
    echo [OK] Copy successful
) else (
    echo [ERROR] Copy failed
)

exit /b %errorlevel%
