@echo off
setlocal enabledelayedexpansion

REM GraalVM Metadata Compare and Integrity Check Tool
REM Purpose: Compare new collected metadata with existing metadata in resources

set "NEW_METADATA_DIR=metadata\META-INF\native-image"
set "OLD_METADATA_DIR=src\main\resources\META-INF\native-image"

echo === GraalVM Metadata Compare Tool ===
echo.

REM Check if directories exist
if not exist "%NEW_METADATA_DIR%" (
    echo [ERROR] New metadata directory not found: %NEW_METADATA_DIR%
    echo Please run your program to collect metadata first
    pause
    exit /b 1
)

if not exist "%OLD_METADATA_DIR%" (
    echo [INFO] No metadata in resources directory, this is first time collection
    set "first_time=true"
) else (
    set "first_time=false"
)

echo [INFO] New metadata directory: %NEW_METADATA_DIR%
echo [INFO] Old metadata directory: %OLD_METADATA_DIR%
echo.

REM Check new metadata integrity
echo === Check New Metadata Integrity ===
call :check_metadata_integrity "%NEW_METADATA_DIR%"
if !errorlevel! neq 0 (
    echo [WARN] New metadata may be incomplete or corrupted!
    pause
)

REM If not first time, compare differences
if "%first_time%"=="false" (
    echo.
    echo === Compare Metadata Differences ===
    call :compare_metadata
)

echo.
echo === Summary ===
call :show_summary

echo.
echo Copy new metadata to resources directory? (y/n):
set /p choice=
if /i "%choice%"=="y" (
    call :copy_metadata
    echo [DONE] Metadata updated to resources directory
) else (
    echo [SKIP] Resources directory not updated
)

pause
exit /b 0

REM ==================== 函数定义 ====================

:check_metadata_integrity
set "dir=%~1"
set "issues=0"

echo Checking directory: %dir%

REM Check required config files
set "required_files=reflect-config.json resource-config.json proxy-config.json jni-config.json serialization-config.json"

for %%f in (%required_files%) do (
    if exist "%dir%\%%f" (
        call :validate_json "%dir%\%%f"
        if !errorlevel! equ 0 (
            call :analyze_json_content "%dir%\%%f"
        ) else (
            echo   [X] %%f - JSON format error
            set /a issues+=1
        )
    ) else (
        echo   [?] %%f - File not found (feature may not be used)
    )
)

if %issues% equ 0 (
    echo [OK] Metadata integrity check passed
) else (
    echo [WARN] Found %issues% issues
)

exit /b %issues%

:validate_json
set "file=%~1"
if not exist "%file%" exit /b 1

REM 简单的JSON格式检查
set /p first_line=<"%file%"
set "first_char=%first_line:~0,1%"

if "%first_char%"=="{" goto :check_json_end
if "%first_char%"=="[" goto :check_json_end
exit /b 1

:check_json_end
findstr /e "}" "%file%" >nul 2>&1
if %errorlevel% equ 0 exit /b 0
findstr /e "]" "%file%" >nul 2>&1
if %errorlevel% equ 0 exit /b 0
exit /b 1

:analyze_json_content
set "file=%~1"
set "filename="
for %%f in ("%file%") do set "filename=%%~nxf"

REM Get file size
for %%f in ("%file%") do set "size=%%~zf"

REM Count entries (simplified)
for /f %%i in ('type "%file%" ^| find /c ":"') do set "entries=%%i"

if %size% lss 10 (
    echo   [+] %filename% - Very small file ^(%size% bytes, ~%entries% entries^)
) else if %entries% lss 5 (
    echo   [+] %filename% - Few entries ^(%size% bytes, ~%entries% entries^)
) else (
    echo   [+] %filename% - Normal ^(%size% bytes, ~%entries% entries^)
)
exit /b 0

:compare_metadata
set "differences=0"

echo Comparing config file differences...

for %%f in (reflect-config.json resource-config.json proxy-config.json jni-config.json serialization-config.json) do (
    if exist "%NEW_METADATA_DIR%\%%f" (
        if exist "%OLD_METADATA_DIR%\%%f" (
            fc /b "%NEW_METADATA_DIR%\%%f" "%OLD_METADATA_DIR%\%%f" >nul 2>&1
            if !errorlevel! neq 0 (
                echo   [DIFF] %%f - File changed
                call :show_file_diff "%%f"
                set /a differences+=1
            ) else (
                echo   [SAME] %%f - File identical
            )
        ) else (
            echo   [NEW] %%f - New file
            set /a differences+=1
        )
    ) else (
        if exist "%OLD_METADATA_DIR%\%%f" (
            echo   [DEL] %%f - File deleted
            set /a differences+=1
        )
    )
)

if %differences% equ 0 (
    echo [INFO] All files are identical, no update needed
) else (
    echo [INFO] Found %differences% files with differences
)

exit /b 0

:show_file_diff
set "filename=%~1"
set "new_file=%NEW_METADATA_DIR%\%filename%"
set "old_file=%OLD_METADATA_DIR%\%filename%"

for %%f in ("%new_file%") do set "new_size=%%~zf"
for %%f in ("%old_file%") do set "old_size=%%~zf"

if %new_size% gtr %old_size% (
    set /a diff_size=%new_size%-%old_size%
    echo     File increased by !diff_size! bytes
) else if %new_size% lss %old_size% (
    set /a diff_size=%old_size%-%new_size%
    echo     File decreased by !diff_size! bytes
) else (
    echo     Same size but different content
)

exit /b 0

:show_summary
echo.
echo Metadata Statistics:

REM 统计新元数据
if exist "%NEW_METADATA_DIR%" (
    set "new_files=0"
    set "new_total_size=0"
    
    for %%f in ("%NEW_METADATA_DIR%\*.json") do (
        set /a new_files+=1
        for %%s in ("%%f") do set /a new_total_size+=%%~zs
    )
    
    echo   New metadata: !new_files! files, total size !new_total_size! bytes
)

REM 统计旧元数据
if exist "%OLD_METADATA_DIR%" (
    set "old_files=0"
    set "old_total_size=0"

    for %%f in ("%OLD_METADATA_DIR%\*.json") do (
        set /a old_files+=1
        for %%s in ("%%f") do set /a old_total_size+=%%~zs
    )

    echo   Old metadata: !old_files! files, total size !old_total_size! bytes
) else (
    echo   Old metadata: None (first time)
)

exit /b 0

:copy_metadata
echo Copying metadata...

if not exist "%OLD_METADATA_DIR%" mkdir "%OLD_METADATA_DIR%"

xcopy "%NEW_METADATA_DIR%\*" "%OLD_METADATA_DIR%\" /Y /Q >nul 2>&1

if %errorlevel% equ 0 (
    echo [OK] Copy successful
) else (
    echo [ERROR] Copy failed
)

exit /b %errorlevel%
