#!/bin/bash

# GraalVM 元数据验证和备份脚本
# 用途：在构建前验证元数据文件的完整性，并提供备份恢复功能

METADATA_DIR="metadata/META-INF/native-image"
BACKUP_DIR="metadata-backup"
RESOURCES_METADATA_DIR="src/main/resources/META-INF/native-image"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 验证JSON文件格式
validate_json() {
    local file="$1"
    if command -v jq >/dev/null 2>&1; then
        if jq empty "$file" >/dev/null 2>&1; then
            return 0
        else
            return 1
        fi
    else
        # 简单的JSON格式检查（如果没有jq）
        if [[ $(head -c 1 "$file") == "{" || $(head -c 1 "$file") == "[" ]] && 
           [[ $(tail -c 2 "$file" | head -c 1) == "}" || $(tail -c 2 "$file" | head -c 1) == "]" ]]; then
            return 0
        else
            return 1
        fi
    fi
}

# 创建备份
create_backup() {
    if [[ -d "$METADATA_DIR" ]]; then
        log_info "创建元数据备份..."
        mkdir -p "$BACKUP_DIR"
        cp -r "$METADATA_DIR" "$BACKUP_DIR/$(date +%Y%m%d_%H%M%S)"
        log_info "备份创建完成: $BACKUP_DIR/$(date +%Y%m%d_%H%M%S)"
    fi
}

# 验证元数据文件
validate_metadata() {
    local dir="$1"
    local valid=true
    
    if [[ ! -d "$dir" ]]; then
        log_warn "元数据目录不存在: $dir"
        return 1
    fi
    
    log_info "验证元数据文件: $dir"
    
    # 检查所有JSON文件
    while IFS= read -r -d '' file; do
        if ! validate_json "$file"; then
            log_error "JSON格式错误: $file"
            valid=false
        else
            log_info "✓ $file"
        fi
    done < <(find "$dir" -name "*.json" -print0)
    
    if $valid; then
        log_info "所有元数据文件验证通过"
        return 0
    else
        log_error "发现损坏的元数据文件"
        return 1
    fi
}

# 恢复最新备份
restore_backup() {
    local latest_backup=$(ls -1t "$BACKUP_DIR" 2>/dev/null | head -n 1)
    
    if [[ -n "$latest_backup" ]]; then
        log_info "恢复最新备份: $latest_backup"
        rm -rf "$METADATA_DIR"
        cp -r "$BACKUP_DIR/$latest_backup/native-image" "$METADATA_DIR"
        log_info "备份恢复完成"
    else
        log_error "没有找到可用的备份"
        return 1
    fi
}

# 安全复制到resources
safe_copy_to_resources() {
    if validate_metadata "$METADATA_DIR"; then
        log_info "复制验证通过的元数据到resources目录..."
        mkdir -p "$RESOURCES_METADATA_DIR"
        cp -r "$METADATA_DIR"/* "$RESOURCES_METADATA_DIR/"
        log_info "复制完成"
    else
        log_error "元数据验证失败，不复制到resources目录"
        return 1
    fi
}

# 主函数
main() {
    case "${1:-validate}" in
        "backup")
            create_backup
            ;;
        "validate")
            validate_metadata "$METADATA_DIR"
            ;;
        "restore")
            restore_backup
            ;;
        "copy")
            safe_copy_to_resources
            ;;
        "full")
            create_backup
            if validate_metadata "$METADATA_DIR"; then
                safe_copy_to_resources
            else
                log_error "验证失败，是否要恢复备份？(y/n)"
                read -r response
                if [[ "$response" == "y" ]]; then
                    restore_backup
                    safe_copy_to_resources
                fi
            fi
            ;;
        *)
            echo "用法: $0 {backup|validate|restore|copy|full}"
            echo "  backup   - 创建元数据备份"
            echo "  validate - 验证元数据文件"
            echo "  restore  - 恢复最新备份"
            echo "  copy     - 安全复制到resources"
            echo "  full     - 完整流程（备份+验证+复制）"
            exit 1
            ;;
    esac
}

main "$@"
