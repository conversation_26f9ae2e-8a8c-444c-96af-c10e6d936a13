{"resources": {"includes": [{"pattern": "\\QMETA-INF/graalvm/org.graalvm.polyglot/version\\E"}, {"pattern": "\\QMETA-INF/graalvm/org.graalvm.truffle/version\\E"}, {"pattern": "\\QMETA-INF/services/ch.qos.logback.classic.spi.Configurator\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.TruffleLanguage$Provider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.TruffleRuntimeAccess\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.impl.TruffleLocator\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.instrumentation.TruffleInstrument$Provider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.instrumentation.provider.TruffleInstrumentProvider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.library.DefaultExportProvider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.library.provider.DefaultExportProvider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.object.LayoutFactory\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.provider.InternalResourceProvider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.api.provider.TruffleLanguageProvider\\E"}, {"pattern": "\\QMETA-INF/services/com.oracle.truffle.js.runtime.Evaluator\\E"}, {"pattern": "\\QMETA-INF/services/java.awt.im.spi.InputMethodDescriptor\\E"}, {"pattern": "\\QMETA-INF/services/java.lang.System$LoggerFinder\\E"}, {"pattern": "\\QMETA-INF/services/java.net.spi.URLStreamHandlerProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.nio.channels.spi.SelectorProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.sql.Driver\\E"}, {"pattern": "\\QMETA-INF/services/java.time.zone.ZoneRulesProvider\\E"}, {"pattern": "\\QMETA-INF/services/java.util.prefs.PreferencesFactory\\E"}, {"pattern": "\\QMETA-INF/services/java.util.spi.ResourceBundleControlProvider\\E"}, {"pattern": "\\QMETA-INF/services/javax.script.ScriptEngineFactory\\E"}, {"pattern": "\\QMETA-INF/services/javax.xml.parsers.DocumentBuilderFactory\\E"}, {"pattern": "\\QMETA-INF/services/javax.xml.parsers.SAXParserFactory\\E"}, {"pattern": "\\QMETA-INF/services/org.graalvm.home.HomeFinder\\E"}, {"pattern": "\\QMETA-INF/services/org.graalvm.polyglot.impl.AbstractPolyglotImpl\\E"}, {"pattern": "\\QMETA-INF/services/org.slf4j.spi.SLF4JServiceProvider\\E"}, {"pattern": "\\Q\\E"}, {"pattern": "\\Qcom/ibm/icu/ICUConfig.properties\\E"}, {"pattern": "\\Qcom/ibm/icu/impl/data/icudt72b/pool.res\\E"}, {"pattern": "\\Qcom/ibm/icu/impl/data/icudt72b/root.res\\E"}, {"pattern": "\\Qcom/ibm/icu/impl/data/icudt72b/zoneinfo64.res\\E"}, {"pattern": "\\Qlogback-test.scmo\\E"}, {"pattern": "\\Qlogback-test.xml\\E"}, {"pattern": "\\Qlogback.scmo\\E"}, {"pattern": "\\Qlogback.xml\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/nfkc.nrm\\E"}, {"pattern": "java.base:\\Qjdk/internal/icu/impl/data/icudt72b/ubidi.icu\\E"}, {"pattern": "java.datatransfer:\\Qsun/datatransfer/resources/flavormap.properties\\E"}, {"pattern": "java.desktop:\\Qjavax/swing/plaf/metal/icons/ocean/error.png\\E"}, {"pattern": "java.desktop:\\Qjavax/swing/plaf/metal/icons/ocean/question.png\\E"}, {"pattern": "jdk.jfr:\\Qjdk/jfr/internal/query/view.ini\\E"}]}, "bundles": [{"name": "com.mysql.jdbc.LocalizedErrorMessages", "locales": ["", "und"]}, {"name": "com.sun.swing.internal.plaf.basic.resources.basic", "classNames": ["com.sun.swing.internal.plaf.basic.resources.basic", "com.sun.swing.internal.plaf.basic.resources.basic_zh_CN"]}, {"name": "com.sun.swing.internal.plaf.metal.resources.metal", "classNames": ["com.sun.swing.internal.plaf.metal.resources.metal", "com.sun.swing.internal.plaf.metal.resources.metal_zh_CN"]}, {"name": "sun.awt.resources.awt", "classNames": ["sun.awt.resources.awt", "sun.awt.resources.awt_zh_CN"]}]}