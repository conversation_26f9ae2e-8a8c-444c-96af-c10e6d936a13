@echo off
setlocal enabledelayedexpansion

REM GraalVM 元数据验证和备份脚本 (Windows版本)
REM 用途：在构建前验证元数据文件的完整性，并提供备份恢复功能

set "METADATA_DIR=metadata\META-INF\native-image"
set "BACKUP_DIR=metadata-backup"
set "RESOURCES_METADATA_DIR=src\main\resources\META-INF\native-image"

REM 获取当前时间戳
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%%dt:~4,2%%dt:~6,2%_%dt:~8,2%%dt:~10,2%%dt:~12,2%"

REM 日志函数
:log_info
echo [INFO] %~1
goto :eof

:log_warn
echo [WARN] %~1
goto :eof

:log_error
echo [ERROR] %~1
goto :eof

REM 简单的JSON格式验证
:validate_json
set "file=%~1"
if not exist "%file%" (
    call :log_error "文件不存在: %file%"
    exit /b 1
)

REM 检查文件是否以 { 或 [ 开头，以 } 或 ] 结尾
set /p first_char=<"%file%"
set "first_char=%first_char:~0,1%"

REM 获取最后一个字符（简化版本）
for /f %%i in ('type "%file%" ^| find /c /v ""') do set line_count=%%i
if %line_count% equ 0 (
    call :log_error "空文件: %file%"
    exit /b 1
)

if "%first_char%"=="{" goto check_end
if "%first_char%"=="[" goto check_end
call :log_error "JSON格式错误 - 开头字符: %file%"
exit /b 1

:check_end
REM 简化的结尾检查
findstr /e "}" "%file%" >nul 2>&1
if %errorlevel% equ 0 goto json_valid
findstr /e "]" "%file%" >nul 2>&1
if %errorlevel% equ 0 goto json_valid
call :log_error "JSON格式错误 - 结尾字符: %file%"
exit /b 1

:json_valid
exit /b 0

REM 创建备份
:create_backup
if exist "%METADATA_DIR%" (
    call :log_info "创建元数据备份..."
    if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
    xcopy "%METADATA_DIR%" "%BACKUP_DIR%\%timestamp%\native-image\" /E /I /Q >nul
    call :log_info "备份创建完成: %BACKUP_DIR%\%timestamp%"
) else (
    call :log_warn "元数据目录不存在: %METADATA_DIR%"
)
goto :eof

REM 验证元数据文件
:validate_metadata
set "dir=%~1"
set "valid=true"

if not exist "%dir%" (
    call :log_warn "元数据目录不存在: %dir%"
    exit /b 1
)

call :log_info "验证元数据文件: %dir%"

REM 检查所有JSON文件
for /r "%dir%" %%f in (*.json) do (
    call :validate_json "%%f"
    if !errorlevel! neq 0 (
        call :log_error "JSON格式错误: %%f"
        set "valid=false"
    ) else (
        call :log_info "✓ %%f"
    )
)

if "%valid%"=="true" (
    call :log_info "所有元数据文件验证通过"
    exit /b 0
) else (
    call :log_error "发现损坏的元数据文件"
    exit /b 1
)

REM 恢复最新备份
:restore_backup
if not exist "%BACKUP_DIR%" (
    call :log_error "备份目录不存在"
    exit /b 1
)

REM 找到最新的备份目录
set "latest_backup="
for /f "delims=" %%d in ('dir "%BACKUP_DIR%" /b /ad /o-d 2^>nul') do (
    if not defined latest_backup set "latest_backup=%%d"
)

if defined latest_backup (
    call :log_info "恢复最新备份: %latest_backup%"
    if exist "%METADATA_DIR%" rmdir /s /q "%METADATA_DIR%"
    xcopy "%BACKUP_DIR%\%latest_backup%\native-image" "%METADATA_DIR%\" /E /I /Q >nul
    call :log_info "备份恢复完成"
) else (
    call :log_error "没有找到可用的备份"
    exit /b 1
)
goto :eof

REM 安全复制到resources
:safe_copy_to_resources
call :validate_metadata "%METADATA_DIR%"
if %errorlevel% equ 0 (
    call :log_info "复制验证通过的元数据到resources目录..."
    if not exist "%RESOURCES_METADATA_DIR%" mkdir "%RESOURCES_METADATA_DIR%"
    xcopy "%METADATA_DIR%\*" "%RESOURCES_METADATA_DIR%\" /E /Y /Q >nul
    call :log_info "复制完成"
) else (
    call :log_error "元数据验证失败，不复制到resources目录"
    exit /b 1
)
goto :eof

REM 完整流程
:full_process
call :create_backup
call :validate_metadata "%METADATA_DIR%"
if %errorlevel% equ 0 (
    call :safe_copy_to_resources
) else (
    call :log_error "验证失败，是否要恢复备份？(y/n)"
    set /p response=
    if /i "!response!"=="y" (
        call :restore_backup
        if !errorlevel! equ 0 call :safe_copy_to_resources
    )
)
goto :eof

REM 主函数
set "action=%~1"
if "%action%"=="" set "action=validate"

if "%action%"=="backup" (
    call :create_backup
) else if "%action%"=="validate" (
    call :validate_metadata "%METADATA_DIR%"
) else if "%action%"=="restore" (
    call :restore_backup
) else if "%action%"=="copy" (
    call :safe_copy_to_resources
) else if "%action%"=="full" (
    call :full_process
) else (
    echo 用法: %0 {backup^|validate^|restore^|copy^|full}
    echo   backup   - 创建元数据备份
    echo   validate - 验证元数据文件
    echo   restore  - 恢复最新备份
    echo   copy     - 安全复制到resources
    echo   full     - 完整流程（备份+验证+复制）
    exit /b 1
)

exit /b %errorlevel%
