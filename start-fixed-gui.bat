@echo off
echo.
echo ========================================
echo   GraalVM 元数据管理工具 - 修复版
echo ========================================
echo.

echo [1/2] 检查编译状态...
if not exist "src\main\java\tools\MetadataChecker.class" (
    echo 正在编译...
    javac -encoding UTF-8 -cp . src/main/java/tools/MetadataChecker.java
    
    if %errorlevel% neq 0 (
        echo 编译失败!
        pause
        exit /b 1
    )
    echo 编译成功!
) else (
    echo 已编译完成!
)

echo.
echo [2/2] 启动GUI工具...
echo.
echo 启动 GraalVM 元数据管理工具...
echo 修复内容:
echo - 移除可能导致乱码的特殊符号
echo - 保持所有中文界面和功能
echo - 使用 [标签] 格式替代特殊字符
echo - 完整性检查显示详细路径
echo - JSON差异行级分析
echo.

java -Dfile.encoding=UTF-8 -cp src/main/java tools.MetadataChecker

echo.
echo 工具已关闭
pause
