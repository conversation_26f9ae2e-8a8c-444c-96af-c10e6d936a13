[{"name": "[Lcom.sun.management.internal.DiagnosticCommandInfo;"}, {"name": "[Lcom.sun.management.internal.DiagnosticCommandArgumentInfo;"}, {"name": "[Ljava.util.Locale;"}, {"name": "[Lsun.java2d.loops.GraphicsPrimitive;"}, {"name": "com.sun.management.internal.DiagnosticCommandArgumentInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "boolean", "boolean", "int"]}]}, {"name": "com.sun.management.internal.DiagnosticCommandInfo", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String", "boolean", "java.util.List"]}]}, {"name": "java.awt.AWTEvent", "fields": [{"name": "bdata"}, {"name": "consumed"}, {"name": "id"}]}, {"name": "java.awt.AlphaComposite", "fields": [{"name": "extraAlpha"}, {"name": "rule"}]}, {"name": "java.awt.Color", "methods": [{"name": "getRGB", "parameterTypes": []}]}, {"name": "java.awt.Component", "fields": [{"name": "appContext"}, {"name": "background"}, {"name": "cursor"}, {"name": "enabled"}, {"name": "focusable"}, {"name": "foreground"}, {"name": "graphicsConfig"}, {"name": "height"}, {"name": "parent"}, {"name": "peer"}, {"name": "visible"}, {"name": "width"}, {"name": "x"}, {"name": "y"}], "methods": [{"name": "getFont_NoClientCode", "parameterTypes": []}, {"name": "getLocationOnScreen_NoTreeLock", "parameterTypes": []}, {"name": "getToolkitImpl", "parameterTypes": []}, {"name": "isEnabledImpl", "parameterTypes": []}]}, {"name": "java.awt.Container", "fields": [{"name": "layoutMgr"}]}, {"name": "java.awt.Cursor", "fields": [{"name": "pData"}, {"name": "type"}], "methods": [{"name": "setPData", "parameterTypes": ["long"]}]}, {"name": "java.awt.Dialog", "fields": [{"name": "title"}, {"name": "undecorated"}]}, {"name": "java.awt.Dimension", "fields": [{"name": "height"}, {"name": "width"}], "methods": [{"name": "<init>", "parameterTypes": ["int", "int"]}]}, {"name": "java.awt.Font", "fields": [{"name": "name"}, {"name": "pData"}, {"name": "size"}, {"name": "style"}], "methods": [{"name": "getFont", "parameterTypes": ["java.lang.String"]}, {"name": "getFontPeer", "parameterTypes": []}]}, {"name": "java.awt.FontMetrics", "fields": [{"name": "font"}], "methods": [{"name": "getHeight", "parameterTypes": []}]}, {"name": "java.awt.Frame", "fields": [{"name": "undecorated"}]}, {"name": "java.awt.Insets", "fields": [{"name": "bottom"}, {"name": "left"}, {"name": "right"}, {"name": "top"}], "methods": [{"name": "<init>", "parameterTypes": ["int", "int", "int", "int"]}]}, {"name": "java.awt.Point", "fields": [{"name": "x"}, {"name": "y"}], "methods": [{"name": "<init>", "parameterTypes": ["int", "int"]}]}, {"name": "java.awt.Rectangle", "methods": [{"name": "<init>", "parameterTypes": ["int", "int", "int", "int"]}]}, {"name": "java.awt.SequencedEvent", "methods": [{"name": "<init>", "parameterTypes": ["java.awt.AWTEvent"]}]}, {"name": "java.awt.Toolkit", "methods": [{"name": "getDefaultToolkit", "parameterTypes": []}, {"name": "getFontMetrics", "parameterTypes": ["java.awt.Font"]}]}, {"name": "java.awt.Window", "fields": [{"name": "autoRequestFocus"}, {"name": "locationByPlatform"}, {"name": "securityWarningHeight"}, {"name": "securityWarningWidth"}, {"name": "warningString"}], "methods": [{"name": "calculateSecurityWarningPosition", "parameterTypes": ["double", "double", "double", "double"]}, {"name": "getWarningString", "parameterTypes": []}]}, {"name": "java.awt.Window$Type"}, {"name": "java.awt.desktop.UserSessionEvent$Reason", "fields": [{"name": "CONSOLE"}, {"name": "LOCK"}, {"name": "REMOTE"}, {"name": "UNSPECIFIED"}]}, {"name": "java.awt.event.ComponentEvent", "methods": [{"name": "<init>", "parameterTypes": ["java.awt.Component", "int"]}]}, {"name": "java.awt.event.InputEvent", "fields": [{"name": "modifiers"}], "methods": [{"name": "getButtonDownMasks", "parameterTypes": []}]}, {"name": "java.awt.event.KeyEvent", "fields": [{"name": "extendedKeyCode"}, {"name": "keyChar"}, {"name": "keyCode"}, {"name": "primaryLevelUnicode"}, {"name": "rawCode"}, {"name": "scancode"}], "methods": [{"name": "<init>", "parameterTypes": ["java.awt.Component", "int", "long", "int", "int", "char", "int"]}]}, {"name": "java.awt.event.MouseEvent", "fields": [{"name": "button"}, {"name": "causedByTouchEvent"}, {"name": "x"}, {"name": "y"}], "methods": [{"name": "<init>", "parameterTypes": ["java.awt.Component", "int", "long", "int", "int", "int", "int", "int", "int", "boolean", "int"]}]}, {"name": "java.awt.geom.AffineTransform", "fields": [{"name": "m00"}, {"name": "m01"}, {"name": "m02"}, {"name": "m10"}, {"name": "m11"}, {"name": "m12"}]}, {"name": "java.awt.geom.GeneralPath", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["int", "byte[]", "int", "float[]", "int"]}]}, {"name": "java.awt.geom.Path2D", "fields": [{"name": "numTypes"}, {"name": "pointTypes"}, {"name": "windingRule"}]}, {"name": "java.awt.geom.Path2D$Float", "fields": [{"name": "floatCoords"}]}, {"name": "java.awt.geom.Point2D$Float", "fields": [{"name": "x"}, {"name": "y"}], "methods": [{"name": "<init>", "parameterTypes": ["float", "float"]}]}, {"name": "java.awt.geom.Rectangle2D$Float", "fields": [{"name": "height"}, {"name": "width"}, {"name": "x"}, {"name": "y"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<init>", "parameterTypes": ["float", "float", "float", "float"]}]}, {"name": "java.awt.image.BufferedImage", "fields": [{"name": "colorModel"}, {"name": "imageType"}, {"name": "raster"}], "methods": [{"name": "getRGB", "parameterTypes": ["int", "int", "int", "int", "int[]", "int", "int"]}, {"name": "setRGB", "parameterTypes": ["int", "int", "int", "int", "int[]", "int", "int"]}]}, {"name": "java.awt.image.ColorModel", "fields": [{"name": "colorSpace"}, {"name": "colorSpaceType"}, {"name": "isAlphaPremultiplied"}, {"name": "is_sRGB"}, {"name": "nBits"}, {"name": "numComponents"}, {"name": "supportsAlpha"}, {"name": "transparency"}], "methods": [{"name": "getRGBdefault", "parameterTypes": []}]}, {"name": "java.awt.image.DirectColorModel", "methods": [{"name": "<init>", "parameterTypes": ["int", "int", "int", "int"]}]}, {"name": "java.awt.image.IndexColorModel", "fields": [{"name": "allgrayopaque"}, {"name": "colorData"}, {"name": "lookupcache"}, {"name": "map_size"}, {"name": "rgb"}, {"name": "transparent_index"}]}, {"name": "java.awt.image.Raster", "fields": [{"name": "dataBuffer"}, {"name": "height"}, {"name": "minX"}, {"name": "minY"}, {"name": "numBands"}, {"name": "numDataElements"}, {"name": "sampleModel"}, {"name": "sampleModelTranslateX"}, {"name": "sampleModelTranslateY"}, {"name": "width"}]}, {"name": "java.awt.image.SampleModel", "fields": [{"name": "height"}, {"name": "width"}], "methods": [{"name": "getPixels", "parameterTypes": ["int", "int", "int", "int", "int[]", "java.awt.image.DataBuffer"]}, {"name": "setPixels", "parameterTypes": ["int", "int", "int", "int", "int[]", "java.awt.image.DataBuffer"]}]}, {"name": "java.awt.image.SinglePixelPackedSampleModel", "fields": [{"name": "bitMasks"}, {"name": "bitOffsets"}, {"name": "bitSizes"}, {"name": "maxBitSize"}]}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.Enum", "methods": [{"name": "name", "parameterTypes": []}]}, {"name": "java.lang.InternalError", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String"]}]}, {"name": "java.lang.String", "methods": [{"name": "toLowerCase", "parameterTypes": ["java.util.Locale"]}]}, {"name": "java.lang.Thread", "methods": [{"name": "currentThread", "parameterTypes": []}]}, {"name": "java.util.ArrayList", "methods": [{"name": "<init>", "parameterTypes": ["int"]}, {"name": "add", "parameterTypes": ["java.lang.Object"]}]}, {"name": "java.util.Arrays", "methods": [{"name": "asList", "parameterTypes": ["java.lang.Object[]"]}]}, {"name": "java.util.HashMap", "methods": [{"name": "<PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.Object"]}, {"name": "put", "parameterTypes": ["java.lang.Object", "java.lang.Object"]}]}, {"name": "java.util.Locale", "methods": [{"name": "forLanguageTag", "parameterTypes": ["java.lang.String"]}]}, {"name": "sun.awt.AWTAutoShutdown", "methods": [{"name": "notifyToolkitThreadBusy", "parameterTypes": []}, {"name": "notifyToolkitThreadFree", "parameterTypes": []}]}, {"name": "sun.awt.EmbeddedFrame"}, {"name": "sun.awt.ExtendedKeyCodes", "methods": [{"name": "getExtendedKeyCodeForChar", "parameterTypes": ["int"]}]}, {"name": "sun.awt.FontDescriptor", "fields": [{"name": "nativeName"}, {"name": "useUnicode"}]}, {"name": "sun.awt.LightweightFrame"}, {"name": "sun.awt.PlatformFont", "fields": [{"name": "componentFonts"}, {"name": "fontConfig"}], "methods": [{"name": "makeConvertedMultiFontString", "parameterTypes": ["java.lang.String"]}]}, {"name": "sun.awt.SunHints", "fields": [{"name": "INTVAL_STROKE_PURE"}]}, {"name": "sun.awt.SunToolkit", "methods": [{"name": "isTouchKeyboardAutoShowEnabled", "parameterTypes": []}]}, {"name": "sun.awt.TimedWindowEvent", "methods": [{"name": "<init>", "parameterTypes": ["java.awt.Window", "int", "java.awt.Window", "int", "int", "long"]}]}, {"name": "sun.awt.UngrabEvent", "methods": [{"name": "<init>", "parameterTypes": ["java.awt.Component"]}]}, {"name": "sun.awt.Win32GraphicsConfig", "fields": [{"name": "visual"}]}, {"name": "sun.awt.Win32GraphicsDevice", "fields": [{"name": "dynamicColorModel"}]}, {"name": "sun.awt.Win32GraphicsEnvironment", "methods": [{"name": "dwmCompositionChanged", "parameterTypes": ["boolean"]}]}, {"name": "sun.awt.im.InputMethodWindow"}, {"name": "sun.awt.image.BufImgSurfaceData$ICMColorData", "fields": [{"name": "pData"}], "methods": [{"name": "<init>", "parameterTypes": ["long"]}]}, {"name": "sun.awt.image.ImageRepresentation", "fields": [{"name": "numSrcLUT"}, {"name": "srcLUTtransIndex"}]}, {"name": "sun.awt.image.IntegerComponentRaster", "fields": [{"name": "data"}, {"name": "dataOffsets"}, {"name": "pixelStride"}, {"name": "scanlineStride"}, {"name": "type"}]}, {"name": "sun.awt.image.SunVolatileImage", "fields": [{"name": "volSurfaceManager"}]}, {"name": "sun.awt.image.VolatileSurfaceManager", "fields": [{"name": "sdCurrent"}]}, {"name": "sun.awt.windows.WClipboard", "methods": [{"name": "lostSelectionOwnershipImpl", "parameterTypes": []}]}, {"name": "sun.awt.windows.WComponentPeer", "fields": [{"name": "hwnd"}, {"name": "winGraphicsConfig"}], "methods": [{"name": "disposeLater", "parameterTypes": []}, {"name": "dynamicallyLayoutContainer", "parameterTypes": []}, {"name": "handleExpose", "parameterTypes": ["int", "int", "int", "int"]}, {"name": "postEvent", "parameterTypes": ["java.awt.AWTEvent"]}, {"name": "replaceSurfaceData", "parameterTypes": []}, {"name": "replaceSurfaceDataLater", "parameterTypes": []}]}, {"name": "sun.awt.windows.WDesktopPeer", "methods": [{"name": "systemSleepCallback", "parameterTypes": ["boolean"]}, {"name": "userSessionCallback", "parameterTypes": ["boolean", "java.awt.desktop.UserSessionEvent$Reason"]}]}, {"name": "sun.awt.windows.WDesktopProperties", "fields": [{"name": "pData"}], "methods": [{"name": "setBooleanProperty", "parameterTypes": ["java.lang.String", "boolean"]}, {"name": "setColorProperty", "parameterTypes": ["java.lang.String", "int", "int", "int"]}, {"name": "setFontProperty", "parameterTypes": ["java.lang.String", "java.lang.String", "int", "int"]}, {"name": "setIntegerProperty", "parameterTypes": ["java.lang.String", "int"]}, {"name": "setSoundProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "setStringProperty", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "sun.awt.windows.WDialogPeer"}, {"name": "sun.awt.windows.WFontPeer", "fields": [{"name": "textComponentFontName"}]}, {"name": "sun.awt.windows.WFramePeer", "fields": [{"name": "keepOnMinimize"}], "methods": [{"name": "getExtendedState", "parameterTypes": []}]}, {"name": "sun.awt.windows.WGlobalCursorManager", "methods": [{"name": "nativeUpdateCursor", "parameterTypes": ["java.awt.Component"]}]}, {"name": "sun.awt.windows.WObjectPeer", "fields": [{"name": "createError"}, {"name": "destroyed"}, {"name": "pData"}, {"name": "target"}], "methods": [{"name": "getPeerForTarget", "parameterTypes": ["java.lang.Object"]}]}, {"name": "sun.awt.windows.WPanelPeer", "fields": [{"name": "insets_"}]}, {"name": "sun.awt.windows.WToolkit", "methods": [{"name": "displayChanged", "parameterTypes": []}, {"name": "paletteChanged", "parameterTypes": []}, {"name": "windowsSettingChange", "parameterTypes": []}]}, {"name": "sun.awt.windows.WWindowPeer", "fields": [{"name": "windowType"}], "methods": [{"name": "draggedToNewScreen", "parameterTypes": []}, {"name": "getActiveWindowHandles", "parameterTypes": ["java.awt.Component"]}, {"name": "notifyWindowStateChanged", "parameterTypes": ["int", "int"]}, {"name": "setBackground", "parameterTypes": ["java.awt.Color"]}]}, {"name": "sun.font.CharToGlyphMapper", "methods": [{"name": "charToGlyph", "parameterTypes": ["int"]}]}, {"name": "sun.font.Font2D", "methods": [{"name": "canDisplay", "parameterTypes": ["char"]}, {"name": "charToGlyph", "parameterTypes": ["int"]}, {"name": "charToVariationGlyph", "parameterTypes": ["int", "int"]}, {"name": "getMapper", "parameterTypes": []}, {"name": "getTableBytes", "parameterTypes": ["int"]}]}, {"name": "sun.font.FontStrike", "methods": [{"name": "getGlyphMetrics", "parameterTypes": ["int"]}]}, {"name": "sun.font.FontUtilities", "methods": [{"name": "debugFonts", "parameterTypes": []}]}, {"name": "sun.font.FreetypeFontScaler", "methods": [{"name": "invalidateScaler", "parameterTypes": []}]}, {"name": "sun.font.GlyphLayout$GVData", "fields": [{"name": "_count"}, {"name": "_flags"}, {"name": "_glyphs"}, {"name": "_indices"}, {"name": "_positions"}], "methods": [{"name": "grow", "parameterTypes": []}]}, {"name": "sun.font.GlyphList", "fields": [{"name": "gposx"}, {"name": "gposy"}, {"name": "images"}, {"name": "lcdRGBOrder"}, {"name": "lcdSubPixPos"}, {"name": "len"}, {"name": "positions"}, {"name": "usePositions"}]}, {"name": "sun.font.PhysicalStrike", "fields": [{"name": "pScalerContext"}], "methods": [{"name": "adjustPoint", "parameterTypes": ["java.awt.geom.Point2D$Float"]}, {"name": "getGlyphPoint", "parameterTypes": ["int", "int"]}]}, {"name": "sun.font.StrikeMetrics", "methods": [{"name": "<init>", "parameterTypes": ["float", "float", "float", "float", "float", "float", "float", "float", "float", "float"]}]}, {"name": "sun.font.TrueTypeFont", "methods": [{"name": "readBlock", "parameterTypes": ["java.nio.ByteBuffer", "int", "int"]}, {"name": "readBytes", "parameterTypes": ["int", "int"]}]}, {"name": "sun.font.Type1Font", "methods": [{"name": "readFile", "parameterTypes": ["java.nio.ByteBuffer"]}]}, {"name": "sun.instrument.InstrumentationImpl", "methods": [{"name": "<init>", "parameterTypes": ["long", "boolean", "boolean", "boolean"]}, {"name": "loadClassAndCallAgentmain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "loadClassAndCallPremain", "parameterTypes": ["java.lang.String", "java.lang.String"]}, {"name": "transform", "parameterTypes": ["java.lang.Module", "java.lang.ClassLoader", "java.lang.String", "java.lang.Class", "java.security.ProtectionDomain", "byte[]", "boolean"]}]}, {"name": "sun.java2d.Disposer", "methods": [{"name": "addRecord", "parameterTypes": ["java.lang.Object", "long", "long"]}]}, {"name": "sun.java2d.InvalidPipeException"}, {"name": "sun.java2d.NullSurfaceData"}, {"name": "sun.java2d.SunGraphics2D", "fields": [{"name": "clipRegion"}, {"name": "composite"}, {"name": "eargb"}, {"name": "lcdTextContrast"}, {"name": "pixel"}, {"name": "strokeHint"}]}, {"name": "sun.java2d.SurfaceData", "fields": [{"name": "pData"}, {"name": "valid"}]}, {"name": "sun.java2d.d3d.D3DGraphicsDevice$1", "methods": [{"name": "run", "parameterTypes": []}]}, {"name": "sun.java2d.d3d.D3DRenderQueue$1", "methods": [{"name": "run", "parameterTypes": []}]}, {"name": "sun.java2d.d3d.D3DSurfaceData", "fields": [{"name": "nativeHeight"}, {"name": "nativeWidth"}], "methods": [{"name": "dispose", "parameterTypes": ["long"]}, {"name": "setSurfaceLost", "parameterTypes": ["boolean"]}]}, {"name": "sun.java2d.d3d.D3DSurfaceData$1", "methods": [{"name": "run", "parameterTypes": []}]}, {"name": "sun.java2d.d3d.D3DSurfaceData$D3DWindowSurfaceData"}, {"name": "sun.java2d.loops.Blit", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.BlitBg", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.CompositeType", "fields": [{"name": "AnyAlpha"}, {"name": "Src"}, {"name": "SrcNoEa"}, {"name": "SrcOver"}, {"name": "SrcOverNoEa"}, {"name": "<PERSON><PERSON>"}]}, {"name": "sun.java2d.loops.DrawGlyphList", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawGlyphListAA", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawGlyphListLCD", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawLine", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawParallelogram", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawPath", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawPolygons", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.DrawRect", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.FillParallelogram", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.FillPath", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.FillRect", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.FillSpans", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.GraphicsPrimitive", "fields": [{"name": "pNativePrim"}]}, {"name": "sun.java2d.loops.GraphicsPrimitiveMgr", "methods": [{"name": "register", "parameterTypes": ["sun.java2d.loops.GraphicsPrimitive[]"]}]}, {"name": "sun.java2d.loops.MaskBlit", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.MaskFill", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.ScaledBlit", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.SurfaceType", "fields": [{"name": "Any3Byte"}, {"name": "Any4Byte"}, {"name": "AnyByte"}, {"name": "AnyColor"}, {"name": "AnyInt"}, {"name": "AnyShort"}, {"name": "ByteBinary1Bit"}, {"name": "ByteBinary2Bit"}, {"name": "ByteBinary4Bit"}, {"name": "ByteGray"}, {"name": "ByteIndexed"}, {"name": "ByteIndexedBm"}, {"name": "FourByteAbgr"}, {"name": "FourByteAbgrPre"}, {"name": "Index12Gray"}, {"name": "Index8Gray"}, {"name": "IntArgb"}, {"name": "IntArgbBm"}, {"name": "IntArgbPre"}, {"name": "IntBgr"}, {"name": "IntRgb"}, {"name": "IntRgbx"}, {"name": "OpaqueColor"}, {"name": "ThreeByteBgr"}, {"name": "Ushort4444Argb"}, {"name": "Ushort555Rgb"}, {"name": "Ushort555Rgbx"}, {"name": "Ushort565Rgb"}, {"name": "UshortGray"}, {"name": "UshortIndexed"}]}, {"name": "sun.java2d.loops.TransformHelper", "methods": [{"name": "<init>", "parameterTypes": ["long", "sun.java2d.loops.SurfaceType", "sun.java2d.loops.CompositeType", "sun.java2d.loops.SurfaceType"]}]}, {"name": "sun.java2d.loops.XORComposite", "fields": [{"name": "alphaMask"}, {"name": "xorColor"}, {"name": "xorPixel"}]}, {"name": "sun.java2d.pipe.Region", "fields": [{"name": "bands"}, {"name": "endIndex"}, {"name": "hix"}, {"name": "hiy"}, {"name": "lox"}, {"name": "loy"}]}, {"name": "sun.java2d.pipe.RegionIterator", "fields": [{"name": "curIndex"}, {"name": "numXbands"}, {"name": "region"}]}, {"name": "sun.java2d.pipe.ShapeSpanIterator", "fields": [{"name": "pData"}]}, {"name": "sun.java2d.windows.WindowsFlags", "fields": [{"name": "d3dEnabled"}, {"name": "d3dSet"}, {"name": "offscreenSharingEnabled"}, {"name": "setHighDPIAware"}]}, {"name": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]