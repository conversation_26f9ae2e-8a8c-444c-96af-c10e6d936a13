@echo off
echo.
echo ========================================
echo   GraalVM Metadata Manager - Simple
echo ========================================
echo.

echo [1/2] Checking compilation...
if not exist "src\main\java\tools\MetadataCheckerSimple.class" (
    echo Compiling...
    javac -encoding UTF-8 -cp . src/main/java/tools/MetadataCheckerSimple.java
    
    if %errorlevel% neq 0 (
        echo Compilation failed!
        pause
        exit /b 1
    )
    echo Compilation successful!
) else (
    echo Already compiled!
)

echo.
echo [2/2] Starting GUI...
echo.
echo Starting GraalVM Metadata Manager...
echo Features:
echo - No special characters (avoid encoding issues)
echo - All functionality preserved
echo - English interface
echo - Path configuration with drag & drop
echo - Complete directory copying
echo - Detailed comparison and analysis
echo.

java -cp src/main/java tools.MetadataCheckerSimple

echo.
echo Tool closed
pause
