# GraalVM 元数据管理 GUI 工具 - 增强版

## 🎯 工具简介

这是一个专门为GraalVM Native Image元数据管理设计的增强版图形界面工具，具有以下特色：

- ✨ **自定义路径** - 支持用户自定义所有目录路径
- 🎯 **拖拽支持** - 支持拖拽文件夹到路径配置区域
- 💾 **配置持久化** - 自动保存和恢复路径配置
- 🎨 **美化界面** - 现代化的UI设计和图标
- 🔧 **灵活复制** - 可以复制到任意指定目录

## 🚀 快速启动

### 方法1：双击启动脚本
```
双击 run-metadata-tool.bat
```

### 方法2：手动启动
```cmd
# 编译（首次运行）
javac -cp . src/main/java/tools/MetadataChecker.java

# 运行GUI
java -cp src/main/java tools.MetadataChecker
```

## 📋 界面功能

### 主界面布局
```
┌─────────────────────────────────────────────────────────┐
│                   📂 路径配置区域                        │
│  📁 新元数据目录: [路径输入框] [浏览] (支持拖拽)          │
│  📂 对比元数据目录: [路径输入框] [浏览] (支持拖拽)        │
│  🎯 复制目标目录: [路径输入框] [浏览] (支持拖拽)          │
├─────────────────────┬───────────────────────────────────┤
│   📁 新收集的元数据  │     📂 对比元数据                 │
│                    │                                   │
│  📄 文件列表        │    📄 文件列表                     │
│  📊 大小统计        │    📊 大小统计                     │
│  📅 修改时间        │    📅 修改时间                     │
│                    │                                   │
├─────────────────────┴───────────────────────────────────┤
│  [🔄刷新状态] [🔍检查完整性] [📊对比差异] [📋复制文件]    │
├─────────────────────────────────────────────────────────┤
│                    📝 操作日志区域                       │
│  详细的操作记录和结果                                    │
│  ✅ 成功信息  ❌ 错误信息  ⚠️ 警告信息  ℹ️ 提示信息      │
└─────────────────────────────────────────────────────────┘
```

### 新增功能

#### 📂 路径配置区域
- **自定义路径**：可以手动输入或选择任意目录
- **拖拽支持**：直接拖拽文件夹到输入框
- **浏览按钮**：点击浏览按钮选择目录
- **配置持久化**：路径设置自动保存，下次启动时恢复

#### 🎯 灵活的复制目标
- **自定义目标**：可以复制到任意指定目录
- **不限于Resources**：不再局限于src/main/resources目录
- **路径验证**：自动验证目标路径的有效性

### 按钮功能

#### 🔄 刷新状态
- **功能**：扫描并显示配置的目录中的元数据文件
- **显示内容**：
  - 📄 文件名和大小（自动格式化）
  - 📅 最后修改时间
  - 📊 总文件数和总大小统计
- **使用时机**：更改路径后、收集新元数据后

#### 🔍 检查完整性
- **功能**：验证新收集的元数据文件
- **检查项目**：
  - JSON格式是否正确
  - 文件是否被截断或损坏
  - 文件大小是否合理
- **结果显示**：
  - ✅ 格式正确的文件
  - ⚠️ 文件很小的警告
  - ❌ 格式错误的文件

#### 📊 对比差异
- **功能**：对比两个目录中元数据的差异
- **对比结果**：
  - ✅ 文件相同
  - 🔄 文件有变化（显示大小差异）
  - 🆕 新增文件
  - 🗑️ 删除文件
- **使用场景**：决定是否需要更新元数据

#### 📋 复制文件
- **功能**：将源目录的元数据复制到目标目录
- **灵活性**：支持复制到任意目录
- **安全机制**：
  - 复制前显示详细的确认对话框
  - 自动创建目标目录
  - 安全覆盖现有文件
- **结果反馈**：显示复制的文件数量和状态

## 📝 使用流程

### 1. 收集元数据
在IDEA中设置JVM参数：
```
-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image
```

### 2. 启动GUI工具
```cmd
run-metadata-tool.bat
```

### 3. 检查元数据
1. 点击 **"刷新状态"** 查看收集到的文件
2. 点击 **"检查完整性"** 验证文件格式
3. 查看日志区域的详细信息

### 4. 对比差异（可选）
- 如果之前已有元数据，点击 **"对比差异"**
- 查看哪些文件发生了变化
- 根据变化情况决定是否更新

### 5. 更新元数据
- 点击 **"复制到Resources"**
- 确认复制操作
- 查看复制结果

## 🎨 界面特色

### 直观的文件显示
- 📄 文件图标和名称
- 📊 文件大小（自动格式化为KB/MB）
- 📅 最后修改时间
- 📈 总体统计信息

### 丰富的状态图标
- ✅ 成功/正常
- ❌ 错误/失败
- ⚠️ 警告/注意
- ℹ️ 信息/提示
- 🔄 变化/更新
- 🆕 新增
- 🗑️ 删除

### 详细的操作日志
- 实时显示操作过程
- 彩色图标区分不同类型的消息
- 可滚动查看历史记录
- 支持清除日志功能

## 🛠️ 故障排除

### 常见问题

#### 1. 工具启动失败
**现象**：双击bat文件后闪退
**解决**：
- 确保安装了Java 8+
- 在命令行中手动运行查看错误信息
- 检查文件路径是否正确

#### 2. 找不到元数据目录
**现象**：显示"目录不存在"
**解决**：
- 确认JVM参数设置正确
- 确认程序已运行并收集了元数据
- 检查工作目录是否正确

#### 3. JSON格式错误
**现象**：完整性检查显示格式错误
**解决**：
- 可能是程序被强制结束导致文件截断
- 重新运行程序收集元数据
- 检查磁盘空间是否充足

#### 4. 复制失败
**现象**：复制操作报错
**解决**：
- 检查目标目录权限
- 确保没有其他程序占用文件
- 检查磁盘空间

## 📁 目录结构

```
项目根目录/
├── metadata/                           # 临时收集目录
│   └── META-INF/native-image/
│       ├── reflect-config.json
│       ├── resource-config.json
│       ├── proxy-config.json
│       ├── jni-config.json
│       └── serialization-config.json
├── src/main/resources/                 # 构建用目录
│   └── META-INF/native-image/
├── src/main/java/tools/               # GUI工具源码
│   └── MetadataChecker.java
├── run-metadata-tool.bat             # 启动脚本
└── GUI工具使用说明.md                 # 本文档
```

## 💡 使用技巧

1. **分阶段收集**：运行不同功能模块分别收集元数据
2. **定期检查**：每次添加新功能后检查元数据完整性
3. **备份重要配置**：重要项目建议手动备份元数据文件
4. **查看日志**：遇到问题时仔细查看操作日志
5. **确认更新**：只有在确认元数据正确时才复制到Resources

这个GUI工具让元数据管理变得简单直观，不再需要担心批处理脚本的兼容性问题！
