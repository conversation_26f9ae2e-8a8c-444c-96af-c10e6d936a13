@echo off
echo Starting GraalVM Metadata Checker GUI...

REM Check if compiled class exists
if not exist "src\main\java\tools\MetadataChecker.class" (
    echo Compiling Java source...
    javac -cp . src/main/java/tools/MetadataChecker.java

    if %errorlevel% neq 0 (
        echo Compilation failed!
        pause
        exit /b 1
    )
    echo Compilation successful!
)

REM Run the GUI tool
echo Launching GUI...
java -cp src/main/java tools.MetadataChecker
