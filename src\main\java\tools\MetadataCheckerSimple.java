package tools;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.dnd.*;
import java.io.*;
import java.nio.file.*;
import java.security.MessageDigest;
import java.util.*;
import java.util.List;
import java.util.prefs.Preferences;

/**
 * GraalVM 元数据检查和管理工具 (简化版 - 避免乱码)
 */
public class MetadataCheckerSimple extends JFrame {
    
    // 配置键名
    private static final String PREF_NEW_DIR = "new_metadata_dir";
    private static final String PREF_OLD_DIR = "old_metadata_dir";
    private static final String PREF_TARGET_DIR = "target_dir";
    
    // 默认路径
    private static final String DEFAULT_NEW_DIR = "metadata/META-INF/native-image";
    private static final String DEFAULT_OLD_DIR = "src/main/resources/META-INF/native-image";
    private static final String DEFAULT_TARGET_DIR = "src/main/resources/META-INF/native-image";
    
    // 配置存储
    private Preferences prefs;
    
    // 当前路径
    private String newMetadataDir;
    private String oldMetadataDir;
    private String targetDir;
    
    // UI组件
    private JTextArea logArea;
    private JButton checkButton;
    private JButton compareButton;
    private JButton copyButton;
    private JButton refreshButton;
    
    private JPanel newMetadataPanel;
    private JPanel oldMetadataPanel;
    private JTextField newDirField;
    private JTextField oldDirField;
    private JTextField targetDirField;
    
    public MetadataCheckerSimple() {
        // 初始化配置
        prefs = Preferences.userNodeForPackage(MetadataCheckerSimple.class);
        loadConfiguration();
        
        initializeGUI();
        refreshStatus();
    }
    
    private void loadConfiguration() {
        newMetadataDir = prefs.get(PREF_NEW_DIR, DEFAULT_NEW_DIR);
        oldMetadataDir = prefs.get(PREF_OLD_DIR, DEFAULT_OLD_DIR);
        targetDir = prefs.get(PREF_TARGET_DIR, DEFAULT_TARGET_DIR);
    }
    
    private void saveConfiguration() {
        prefs.put(PREF_NEW_DIR, newMetadataDir);
        prefs.put(PREF_OLD_DIR, oldMetadataDir);
        prefs.put(PREF_TARGET_DIR, targetDir);
    }
    
    private void initializeGUI() {
        setTitle("GraalVM 元数据管理工具");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));
        
        // 顶部路径配置面板
        JPanel pathConfigPanel = createPathConfigPanel();
        mainPanel.add(pathConfigPanel, BorderLayout.NORTH);
        
        // 中间内容面板
        JPanel contentPanel = new JPanel(new GridLayout(1, 2, 15, 0));
        contentPanel.setBorder(new EmptyBorder(10, 0, 10, 0));
        
        // 新元数据面板
        newMetadataPanel = createMetadataPanel("新收集的元数据", newMetadataDir);
        contentPanel.add(newMetadataPanel);

        // 旧元数据面板
        oldMetadataPanel = createMetadataPanel("对比元数据", oldMetadataDir);
        contentPanel.add(oldMetadataPanel);
        
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        
        // 底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        // 日志面板
        JPanel logPanel = createLogPanel();
        
        // 使用分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, mainPanel, logPanel);
        splitPane.setDividerLocation(450);
        splitPane.setResizeWeight(0.75);
        splitPane.setBorder(null);
        
        add(splitPane);
        
        // 设置窗口属性
        setSize(1000, 750);
        setLocationRelativeTo(null);
        setMinimumSize(new Dimension(800, 600));
    }
    
    private JPanel createPathConfigPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder("路径配置");
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 14f));
        panel.setBorder(border);
        panel.setBackground(new Color(248, 249, 250));

        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBackground(new Color(248, 249, 250));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 新元数据路径
        gbc.gridx = 0; gbc.gridy = 0;
        configPanel.add(new JLabel("新元数据目录:"), gbc);
        
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        newDirField = new JTextField(newMetadataDir, 30);
        setupDragAndDrop(newDirField, true);
        configPanel.add(newDirField, gbc);
        
        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton newDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        newDirButton.addActionListener(e -> selectDirectory(newDirField, "选择新元数据目录"));
        configPanel.add(newDirButton, gbc);

        gbc.gridx = 3;
        JButton openNewDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openNewDirButton.addActionListener(e -> openDirectory(newDirField.getText()));
        configPanel.add(openNewDirButton, gbc);

        // 对比元数据路径
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0;
        configPanel.add(new JLabel("对比元数据目录:"), gbc);
        
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        oldDirField = new JTextField(oldMetadataDir, 30);
        setupDragAndDrop(oldDirField, true);
        configPanel.add(oldDirField, gbc);
        
        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton oldDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        oldDirButton.addActionListener(e -> selectDirectory(oldDirField, "选择对比元数据目录"));
        configPanel.add(oldDirButton, gbc);

        gbc.gridx = 3;
        JButton openOldDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openOldDirButton.addActionListener(e -> openDirectory(oldDirField.getText()));
        configPanel.add(openOldDirButton, gbc);

        // 目标路径
        gbc.gridx = 0; gbc.gridy = 2; gbc.weightx = 0;
        configPanel.add(new JLabel("复制目标目录:"), gbc);
        
        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        targetDirField = new JTextField(targetDir, 30);
        setupDragAndDrop(targetDirField, true);
        configPanel.add(targetDirField, gbc);
        
        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton targetDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        targetDirButton.addActionListener(e -> selectDirectory(targetDirField, "选择复制目标目录"));
        configPanel.add(targetDirButton, gbc);

        gbc.gridx = 3;
        JButton openTargetDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openTargetDirButton.addActionListener(e -> openDirectory(targetDirField.getText()));
        configPanel.add(openTargetDirButton, gbc);
        
        panel.add(configPanel, BorderLayout.CENTER);
        
        // 添加路径变化监听器
        newDirField.addActionListener(e -> updatePaths());
        oldDirField.addActionListener(e -> updatePaths());
        targetDirField.addActionListener(e -> updatePaths());
        
        return panel;
    }
    
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(button.getFont().deriveFont(Font.BOLD, 12f));
        button.setPreferredSize(new Dimension(80, 30));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // 添加悬停效果
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.brighter());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });
        
        return button;
    }
    
    private void setupDragAndDrop(JComponent component, boolean isDirectory) {
        component.setDropTarget(new DropTarget() {
            @Override
            public synchronized void drop(DropTargetDropEvent evt) {
                try {
                    evt.acceptDrop(DnDConstants.ACTION_COPY);
                    @SuppressWarnings("unchecked")
                    List<File> droppedFiles = (List<File>) evt.getTransferable()
                            .getTransferData(DataFlavor.javaFileListFlavor);
                    
                    if (!droppedFiles.isEmpty()) {
                        File file = droppedFiles.get(0);
                        String path;
                        
                        if (isDirectory) {
                            path = file.isDirectory() ? file.getAbsolutePath() : file.getParent();
                        } else {
                            path = file.getAbsolutePath();
                        }
                        
                        if (component instanceof JTextField) {
                            ((JTextField) component).setText(path);
                            updatePaths();
                        }
                        
                        log("Drag & Drop: " + path);
                    }
                } catch (Exception ex) {
                    log("Drag & Drop failed: " + ex.getMessage());
                }
            }
        });
    }
    
    private void selectDirectory(JTextField field, String title) {
        JFileChooser chooser = new JFileChooser();
        chooser.setDialogTitle(title);
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        chooser.setCurrentDirectory(new File(field.getText()));
        
        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            String selectedPath = chooser.getSelectedFile().getAbsolutePath();
            field.setText(selectedPath);
            updatePaths();
            log("Selected path: " + selectedPath);
        }
    }
    
    private void openDirectory(String path) {
        if (path == null || path.trim().isEmpty()) {
            log("ERROR: Path is empty");
            return;
        }
        
        File dir = new File(path.trim());
        if (!dir.exists()) {
            log("ERROR: Directory not exists: " + path);
            return;
        }
        
        try {
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().open(dir);
                log("Opened directory: " + path);
            } else {
                // Windows fallback
                Runtime.getRuntime().exec("explorer.exe \"" + dir.getAbsolutePath() + "\"");
                log("Opened directory: " + path);
            }
        } catch (IOException e) {
            log("Failed to open directory: " + e.getMessage());
            JOptionPane.showMessageDialog(this, 
                "Cannot open directory: " + e.getMessage(), 
                "Error", 
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void updatePaths() {
        newMetadataDir = newDirField.getText().trim();
        oldMetadataDir = oldDirField.getText().trim();
        targetDir = targetDirField.getText().trim();
        
        saveConfiguration();
        refreshStatus();
    }
    
    private JPanel createMetadataPanel(String title, String directory) {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder(title);
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 13f));
        panel.setBorder(border);
        panel.setBackground(Color.WHITE);

        JTextArea textArea = new JTextArea(12, 30);
        textArea.setEditable(false);
        textArea.setFont(new Font("Consolas", Font.PLAIN, 12));
        textArea.setBackground(new Color(253, 253, 253));
        textArea.setBorder(new EmptyBorder(8, 8, 8, 8));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setBorder(BorderFactory.createLoweredBevelBorder());

        panel.add(scrollPane, BorderLayout.CENTER);
        return panel;
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 10));
        panel.setBackground(new Color(248, 249, 250));

        refreshButton = createStyledButton("刷新状态", new Color(46, 204, 113));
        checkButton = createStyledButton("检查完整性", new Color(52, 152, 219));
        compareButton = createStyledButton("对比差异", new Color(155, 89, 182));
        copyButton = createStyledButton("复制文件", new Color(231, 76, 60));

        Dimension buttonSize = new Dimension(100, 35);
        refreshButton.setPreferredSize(buttonSize);
        checkButton.setPreferredSize(buttonSize);
        compareButton.setPreferredSize(buttonSize);
        copyButton.setPreferredSize(buttonSize);

        refreshButton.addActionListener(e -> refreshStatus());
        checkButton.addActionListener(e -> checkIntegrity());
        compareButton.addActionListener(e -> compareMetadata());
        copyButton.addActionListener(e -> copyMetadata());

        panel.add(refreshButton);
        panel.add(checkButton);
        panel.add(compareButton);
        panel.add(copyButton);

        return panel;
    }

    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder("操作日志");
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 14f));
        panel.setBorder(border);

        logArea = new JTextArea(8, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font("Consolas", Font.PLAIN, 11));
        logArea.setBackground(new Color(248, 249, 250));

        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);

        panel.add(scrollPane, BorderLayout.CENTER);

        JPanel logButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        JButton clearLogButton = createStyledButton("清除日志", new Color(108, 117, 125));
        clearLogButton.addActionListener(e -> {
            logArea.setText("");
            log("日志已清除");
        });

        logButtonPanel.add(clearLogButton);
        panel.add(logButtonPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void refreshStatus() {
        log("=== 刷新元数据状态 ===");
        updateMetadataPanel(newMetadataPanel, newMetadataDir, "新收集的元数据");
        updateMetadataPanel(oldMetadataPanel, oldMetadataDir, "对比元数据");
        log("状态刷新完成");
        log("");
    }

    private void updateMetadataPanel(JPanel panel, String directory, String title) {
        JTextArea textArea = getTextAreaFromPanel(panel);
        StringBuilder content = new StringBuilder();

        File dir = new File(directory);
        if (!dir.exists()) {
            content.append("目录不存在: ").append(directory).append("\n\n");
            content.append("请先收集元数据或拖拽文件夹到此处");
        } else {
            File[] allFiles = dir.listFiles();
            if (allFiles == null || allFiles.length == 0) {
                content.append("目录存在但为空\n");
            } else {
                int fileCount = 0;
                int dirCount = 0;
                int jsonCount = 0;
                long totalSize = 0;

                // 先显示文件夹
                for (File file : allFiles) {
                    if (file.isDirectory()) {
                        dirCount++;
                        content.append(String.format("[文件夹] %-30s\n", file.getName() + "/"));
                    }
                }

                // 再显示文件
                for (File file : allFiles) {
                    if (file.isFile()) {
                        fileCount++;
                        long size = file.length();
                        totalSize += size;

                        String type = "[文件]";
                        if (file.getName().endsWith(".json")) {
                            type = "[JSON]";
                            jsonCount++;
                        } else if (file.getName().endsWith(".properties")) {
                            type = "[配置]";
                        } else if (file.getName().endsWith(".txt")) {
                            type = "[文本]";
                        }

                        content.append(String.format("%s %-30s %s\n",
                            type, file.getName(), formatFileSize(size)));
                    }
                }

                content.append("\n=== 统计信息 ===\n");
                content.append(String.format("文件夹: %d 个\n", dirCount));
                content.append(String.format("文件: %d 个 (JSON: %d 个)\n", fileCount, jsonCount));
                content.append(String.format("总大小: %s", formatFileSize(totalSize)));
            }
        }

        textArea.setText(content.toString());
    }

    private JTextArea getTextAreaFromPanel(JPanel panel) {
        Component[] components = panel.getComponents();
        for (Component comp : components) {
            if (comp instanceof JScrollPane) {
                JScrollPane scrollPane = (JScrollPane) comp;
                return (JTextArea) scrollPane.getViewport().getView();
            }
        }
        return null;
    }

    private void checkIntegrity() {
        log("=== 检查元数据完整性 ===");
        log("检查目录: " + newMetadataDir);
        log("");

        File newDir = new File(newMetadataDir);
        if (!newDir.exists()) {
            log("错误: 目录不存在");
            return;
        }

        String[] requiredFiles = {
            "reflect-config.json",
            "resource-config.json",
            "proxy-config.json",
            "jni-config.json",
            "serialization-config.json"
        };

        int issues = 0;
        for (String fileName : requiredFiles) {
            File file = new File(newDir, fileName);
            if (file.exists()) {
                if (isValidJson(file)) {
                    long size = file.length();
                    String relativePath = getRelativePath(file);
                    if (size < 10) {
                        log("警告: " + fileName + " - 文件很小 (" + size + " bytes)");
                        log("      路径: " + relativePath);
                    } else {
                        log("正常: " + fileName + " - 格式正确 (" + formatFileSize(size) + ")");
                        log("      路径: " + relativePath);
                    }
                } else {
                    log("错误: " + fileName + " - JSON格式错误!");
                    log("      路径: " + getRelativePath(file));
                    issues++;
                }
            } else {
                log("信息: " + fileName + " - 文件不存在 (可能未使用相关功能)");
            }
        }

        log("");
        if (issues == 0) {
            log("成功: 元数据完整性检查通过!");
        } else {
            log("错误: 发现 " + issues + " 个问题!");
        }
        log("");
    }

    private void compareMetadata() {
        log("=== Compare Metadata ===");

        File newDir = new File(newMetadataDir);
        File oldDir = new File(oldMetadataDir);

        if (!newDir.exists()) {
            log("ERROR: New metadata directory not exists");
            return;
        }

        if (!oldDir.exists()) {
            log("INFO: Compare directory not exists, this is first time collection");
            log("");
            return;
        }

        Set<String> allNames = new HashSet<>();
        File[] newFiles = newDir.listFiles();
        File[] oldFiles = oldDir.listFiles();

        if (newFiles != null) {
            for (File file : newFiles) {
                allNames.add(file.getName());
            }
        }

        if (oldFiles != null) {
            for (File file : oldFiles) {
                allNames.add(file.getName());
            }
        }

        int differences = 0;
        for (String name : allNames) {
            File newFile = new File(newDir, name);
            File oldFile = new File(oldDir, name);

            if (newFile.exists() && oldFile.exists()) {
                if (newFile.isDirectory() && oldFile.isDirectory()) {
                    log("DIR:  " + name + "/ - Folder exists in both sides");
                } else if (newFile.isFile() && oldFile.isFile()) {
                    if (!filesEqual(newFile, oldFile)) {
                        log("DIFF: " + name + " - File changed");
                        long newSize = newFile.length();
                        long oldSize = oldFile.length();
                        long diff = newSize - oldSize;

                        log("      Size: " + formatFileSize(oldSize) + " -> " + formatFileSize(newSize));
                        if (diff > 0) {
                            log("      Increased: +" + formatFileSize(diff));
                        } else if (diff < 0) {
                            log("      Decreased: -" + formatFileSize(Math.abs(diff)));
                        } else {
                            log("      Same size but content different");
                        }

                        if (name.endsWith(".json")) {
                            analyzeJsonDifferences(newFile, oldFile);
                        }

                        differences++;
                    } else {
                        log("SAME: " + name + " - File identical");
                    }
                } else {
                    String newType = newFile.isDirectory() ? "folder" : "file";
                    String oldType = oldFile.isDirectory() ? "folder" : "file";
                    log("TYPE: " + name + " - Type changed (" + oldType + " -> " + newType + ")");
                    differences++;
                }
            } else if (newFile.exists()) {
                String type = newFile.isDirectory() ? "folder" : "file";
                log("NEW:  " + name + (newFile.isDirectory() ? "/" : "") + " - New " + type);
                differences++;
            } else if (oldFile.exists()) {
                String type = oldFile.isDirectory() ? "folder" : "file";
                log("DEL:  " + name + (oldFile.isDirectory() ? "/" : "") + " - Deleted " + type);
                differences++;
            }
        }

        log("");
        if (differences == 0) {
            log("INFO: All files are identical, no update needed");
        } else {
            log("INFO: Found " + differences + " differences");
        }
        log("");
    }

    private void copyMetadata() {
        log("=== Copy Metadata ===");

        File newDir = new File(newMetadataDir);
        if (!newDir.exists()) {
            log("ERROR: Source directory not exists");
            return;
        }

        File[] allFiles = newDir.listFiles();
        if (allFiles == null || allFiles.length == 0) {
            log("ERROR: Source directory is empty");
            return;
        }

        int fileCount = 0;
        int dirCount = 0;
        for (File file : allFiles) {
            if (file.isFile()) {
                fileCount++;
            } else if (file.isDirectory()) {
                dirCount++;
            }
        }

        String message = String.format(
            "Copy all content to target directory?\n\n" +
            "Source: %s\n" +
            "Target: %s\n\n" +
            "Will copy:\n" +
            "• %d files\n" +
            "• %d folders\n\n" +
            "This will overwrite existing files!",
            newMetadataDir, targetDir, fileCount, dirCount
        );

        int result = JOptionPane.showConfirmDialog(
            this,
            message,
            "Confirm Copy",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            log("ERROR: User cancelled copy operation");
            return;
        }

        try {
            File targetDirFile = new File(targetDir);
            if (!targetDirFile.exists()) {
                targetDirFile.mkdirs();
                log("Created target directory: " + targetDir);
            }

            int copiedFiles = 0;
            int copiedDirs = 0;

            for (File file : allFiles) {
                if (file.isFile()) {
                    Path source = file.toPath();
                    Path target = Paths.get(targetDir, file.getName());

                    Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
                    log("Copied file: " + file.getName());
                    copiedFiles++;

                } else if (file.isDirectory()) {
                    Path source = file.toPath();
                    Path target = Paths.get(targetDir, file.getName());

                    copyDirectoryRecursively(source, target);
                    log("Copied folder: " + file.getName() + "/");
                    copiedDirs++;
                }
            }

            log("SUCCESS: Copy completed!");
            log("Files: " + copiedFiles);
            log("Folders: " + copiedDirs);

            SwingUtilities.invokeLater(this::refreshStatus);

        } catch (IOException e) {
            log("ERROR: Copy failed: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "Copy failed: " + e.getMessage(),
                "Error",
                JOptionPane.ERROR_MESSAGE);
        }
        log("");
    }

    // Utility methods
    private void copyDirectoryRecursively(Path source, Path target) throws IOException {
        if (!Files.exists(target)) {
            Files.createDirectories(target);
        }

        try (var stream = Files.walk(source)) {
            stream.forEach(sourcePath -> {
                try {
                    Path targetPath = target.resolve(source.relativize(sourcePath));
                    if (Files.isDirectory(sourcePath)) {
                        if (!Files.exists(targetPath)) {
                            Files.createDirectories(targetPath);
                        }
                    } else {
                        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                    }
                } catch (IOException e) {
                    throw new RuntimeException("Copy failed: " + sourcePath, e);
                }
            });
        }
    }

    private boolean isValidJson(File file) {
        try {
            String content = Files.readString(file.toPath());
            content = content.trim();
            return (content.startsWith("{") && content.endsWith("}")) ||
                   (content.startsWith("[") && content.endsWith("]"));
        } catch (IOException e) {
            return false;
        }
    }

    private boolean filesEqual(File file1, File file2) {
        try {
            byte[] hash1 = getFileHash(file1);
            byte[] hash2 = getFileHash(file2);
            return Arrays.equals(hash1, hash2);
        } catch (Exception e) {
            return false;
        }
    }

    private byte[] getFileHash(File file) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] fileBytes = Files.readAllBytes(file.toPath());
        return md.digest(fileBytes);
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }

    private String getRelativePath(File file) {
        try {
            String currentDir = System.getProperty("user.dir");
            String filePath = file.getAbsolutePath();
            if (filePath.startsWith(currentDir)) {
                return filePath.substring(currentDir.length() + 1);
            }
            return filePath;
        } catch (Exception e) {
            return file.getAbsolutePath();
        }
    }

    private void analyzeJsonDifferences(File newFile, File oldFile) {
        try {
            List<String> newLines = Files.readAllLines(newFile.toPath());
            List<String> oldLines = Files.readAllLines(oldFile.toPath());

            if (newLines.size() != oldLines.size()) {
                log("      Lines: " + oldLines.size() + " -> " + newLines.size());
            }

            int diffLine = -1;
            int maxLines = Math.min(newLines.size(), oldLines.size());

            for (int i = 0; i < maxLines; i++) {
                if (!newLines.get(i).equals(oldLines.get(i))) {
                    diffLine = i + 1;
                    break;
                }
            }

            if (diffLine > 0) {
                log("      First diff at line: " + diffLine);

                String oldLine = oldLines.get(diffLine - 1).trim();
                String newLine = newLines.get(diffLine - 1).trim();

                if (oldLine.length() > 50) oldLine = oldLine.substring(0, 50) + "...";
                if (newLine.length() > 50) newLine = newLine.substring(0, 50) + "...";

                log("      Old: " + oldLine);
                log("      New: " + newLine);
            } else if (newLines.size() != oldLines.size()) {
                if (newLines.size() > oldLines.size()) {
                    log("      Added " + (newLines.size() - oldLines.size()) + " lines");
                } else {
                    log("      Removed " + (oldLines.size() - newLines.size()) + " lines");
                }
            }

        } catch (IOException e) {
            log("      Cannot analyze JSON diff: " + e.getMessage());
        }
    }

    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception e) {
                // Use default look and feel
            }

            new MetadataCheckerSimple().setVisible(true);
        });
    }
}
