# GraalVM Native Image Build Configuration (Corrected Version)
# -----------------------------------------
# 使用方法: 将此文件放置在 'src/main/resources/META-INF/native-image/native-image.properties'
# Maven插件会自动加载此文件中的配置。

# --- Section 1: 主要构建参数 (Args) ---
# 所有的构建参数和运行时参数都通过 "Args" 键来指定。
# 每一个参数都使用一个独立的 "Args" 行。

# --- 构建时参数 ---
Args = -march=x86-64-v3
# 将所有日志相关包 (slf4j, logback) 以及 js 解析器都设置为在运行时初始化，以解决类初始化冲突。
Args = --initialize-at-run-time=org.slf4j,ch.qos.logback.core,ch.qos.logback.classic,io.netty,com.oracle.js.parser

# --- 诊断参数 ---
# 添加类初始化跟踪，以找出是哪个类在构建时错误地触发了日志系统的初始化。
Args = --trace-class-initialization=ch.qos.logback.core.util.StatusPrinter,ch.qos.logback.core.subst.Token,ch.qos.logback.core.rolling.helper.Compressor$1,ch.qos.logback.core.util.Loader,ch.qos.logback.core.util.FileSize,ch.qos.logback.core.status.InfoStatus,ch.qos.logback.core.model.processor.ChainedModelFilter$1,ch.qos.logback.core.subst.NodeToStringTransformer$1,ch.qos.logback.core.rolling.helper.FileNamePattern,ch.qos.logback.core.rolling.helper.RollingCalendar,ch.qos.logback.core.rolling.helper.RollingCalendar$1,ch.qos.logback.core.model.processor.ImplicitModelHandler$1,ch.qos.logback.core.status.StatusBase,ch.qos.logback.classic.PatternLayout,ch.qos.logback.classic.Logger,ch.qos.logback.core.CoreConstants,ch.qos.logback.core.pattern.parser.Parser,com.oracle.js.parser.TokenType,ch.qos.logback.classic.model.processor.LogbackClassicDefaultNestedComponentRules,org.slf4j.LoggerFactory,ch.qos.logback.classic.Level,ch.qos.logback.core.util.Duration,ch.qos.logback.core.model.processor.DefaultProcessor$1,ch.qos.logback.core.subst.Parser$1

Args = --strict-image-heap
Args = --no-fallback
Args = --enable-url-protocols=http,https
Args = --verbose
Args = --optimize=2
Args = -H:Name=${build.name}
Args = -H:Path=D:/funmsServer/bin
Args = -H:NumberOfThreads=18
Args = -H:+BuildReport
Args = -H:+UseG1GC
Args = -H:+AllowVMInspection

# --- 运行时参数 (通过 -R: 标志传递给构建器) ---
# 将最小和最大堆内存设置为相等（24G），可以避免运行时堆扩容带来的性能抖动。
Args = -R:MinHeapSize=24G
Args = -R:MaxHeapSize=24G
# 为G1设置一个目标最大停顿时间（毫秒）。G1会尽力达成这个目标。
Args = -R:MaxGCPauseMillis=200
# 年轻代大小。G1对其管理更智能，但设置一个合理的初始值仍然有益。
Args = -R:MaxNewSize=8G
# 警告: 4m是一个非常大的值。除非明确知道需要，否则建议使用默认值（1m）或注释掉此行。
Args = -R:StackSize=4m


# --- Section 2: 运行时Java系统属性 (JavaArgs) ---
# 这个参数用于设置 -D... 系统属性
JavaArgs = -Djava.util.concurrent.ForkJoinPool.common.parallelism=18


# --- 已移除或不推荐的旧配置说明 ---
# -H:Optimize="3" -> 已改为 --optimize=2。不带PGO的企业版最高就是2。
# -H:MicroArchitecture -> 已被 -march=x86-64-v3 替代，功能重复。
# -R:MaximumHeapSizePercent -> 在明确设置MaxHeapSize后无效。
# -R:MaximumYoungGenerationSizePercent -> 与MaxNewSize功能冲突，保留一个更明确的即可。
# -R:PercentTimeInIncrementalCollection -> 用于旧的增量GC，对于G1不是主要调优参数。
# -H:+AddAllCharsets -> 会显著增大镜像体积。如果你的应用不需要支持所有字符集，建议移除此项，让GraalVM自动检测或手动指定需要的字符集。
# -H:InitialCollectionPolicy, -H:+DumpThreadStacksOnSignal, -H:+IncludeDebugHelperMethods -> 主要用于构建时调试，为减小体积和提升安全性，在生产配置中移除。 