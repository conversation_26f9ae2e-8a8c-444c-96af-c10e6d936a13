# GraalVM 元数据收集和管理指南

## 概述

本指南解决了GraalVM Native Image元数据收集过程中的安全性和可靠性问题，提供了完整的解决方案来避免元数据损坏和丢失。

## 问题分析

### 你的担忧是完全合理的！

1. **程序崩溃风险**：JVM崩溃或程序异常退出可能导致元数据文件损坏
2. **强制结束风险**：强制终止JVM可能导致JSON文件被截断或格式错误
3. **直接写入resources风险**：直接在`src/main/resources`中收集元数据存在数据丢失风险

## 解决方案架构

```
项目根目录/
├── metadata/                          # 临时元数据收集目录（安全）
│   └── META-INF/native-image/
│       ├── reflect-config.json
│       ├── resource-config.json
│       └── ...
├── metadata-backup/                   # 自动备份目录
│   ├── 20241220_143022/
│   ├── 20241220_143156/
│   └── ...
├── src/main/resources/                # 构建时复制目标
│   └── META-INF/native-image/
├── scripts/
│   ├── validate-metadata.sh          # Linux/Mac验证脚本
│   └── validate-metadata.bat         # Windows验证脚本
└── pom.xml                           # 已配置自动化流程
```

## 使用方法

### 1. 元数据收集（推荐方式）

**使用临时目录收集，避免直接污染resources：**

```bash
# 在IDEA中设置JVM参数：
-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image

# 或者命令行运行：
java -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image -jar your-app.jar
```

### 2. 自动化构建流程

**Maven构建时会自动执行以下步骤：**

1. **验证阶段**：检查现有元数据文件完整性
2. **备份阶段**：创建时间戳备份
3. **复制阶段**：将验证通过的元数据复制到resources
4. **构建阶段**：使用resources中的元数据进行native构建

```bash
# 正常构建（会自动验证和复制元数据）
mvn clean package -Pnative

# 跳过元数据验证（不推荐）
mvn clean package -Pnative -DskipMetadataValidation=true
```

### 3. 手动管理脚本

**Linux/Mac：**
```bash
# 创建备份
./scripts/validate-metadata.sh backup

# 验证元数据
./scripts/validate-metadata.sh validate

# 恢复最新备份
./scripts/validate-metadata.sh restore

# 安全复制到resources
./scripts/validate-metadata.sh copy

# 完整流程（推荐）
./scripts/validate-metadata.sh full
```

**Windows：**
```cmd
REM 创建备份
scripts\validate-metadata.bat backup

REM 验证元数据
scripts\validate-metadata.bat validate

REM 恢复最新备份
scripts\validate-metadata.bat restore

REM 安全复制到resources
scripts\validate-metadata.bat copy

REM 完整流程（推荐）
scripts\validate-metadata.bat full
```

## 安全保障机制

### 1. 多层备份保护
- **时间戳备份**：每次构建前自动创建备份
- **版本控制**：保留多个历史版本
- **自动恢复**：验证失败时可自动恢复最新备份

### 2. 完整性验证
- **JSON格式检查**：确保文件格式正确
- **文件完整性**：检查文件是否被截断
- **构建前验证**：只有验证通过的元数据才会用于构建

### 3. 故障恢复
- **自动检测**：构建时自动检测元数据问题
- **交互式恢复**：发现问题时提示用户选择恢复方案
- **零停机恢复**：快速恢复到最近的可用状态

## 最佳实践

### 1. 分阶段收集元数据
```bash
# 第一阶段：基础功能
java -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image -jar app.jar --basic-test

# 第二阶段：高级功能
java -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image -jar app.jar --advanced-test

# 第三阶段：边界情况
java -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image -jar app.jar --edge-cases
```

### 2. 定期验证和清理
```bash
# 每周验证一次元数据完整性
./scripts/validate-metadata.sh validate

# 清理过期备份（保留最近10个）
find metadata-backup -maxdepth 1 -type d | sort -r | tail -n +11 | xargs rm -rf
```

### 3. CI/CD集成
```yaml
# GitHub Actions示例
- name: Validate Metadata
  run: ./scripts/validate-metadata.sh full
  
- name: Build Native Image
  run: mvn clean package -Pnative
```

## 故障排除

### 常见问题

1. **元数据文件损坏**
   ```bash
   # 恢复最新备份
   ./scripts/validate-metadata.sh restore
   ```

2. **构建失败**
   ```bash
   # 检查元数据完整性
   ./scripts/validate-metadata.sh validate
   
   # 如果验证失败，恢复备份
   ./scripts/validate-metadata.sh restore
   ```

3. **脚本执行权限问题**
   ```bash
   # Linux/Mac
   chmod +x scripts/validate-metadata.sh
   
   # Windows
   # 以管理员身份运行PowerShell或CMD
   ```

## 配置选项

### Maven属性
```xml
<!-- 跳过元数据验证 -->
<skipMetadataValidation>true</skipMetadataValidation>

<!-- 自定义脚本路径 -->
<metadata.script.executable>bash</metadata.script.executable>
<metadata.script.name>validate-metadata.sh</metadata.script.name>
```

### JVM参数选项
```bash
# 合并模式（推荐）
-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image

# 覆盖模式（不推荐用于生产）
-agentlib:native-image-agent=config-output-dir=metadata/META-INF/native-image

# 条件收集（仅在特定条件下收集）
-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image,caller-filter-file=filter.json
```

## 总结

通过这套完整的解决方案，你可以：

1. **安全收集**：在临时目录收集元数据，避免污染源码
2. **自动备份**：每次构建前自动备份，防止数据丢失
3. **完整性保证**：构建前验证元数据完整性
4. **快速恢复**：出现问题时快速恢复到可用状态
5. **零配置使用**：Maven集成，无需手动干预

这样既保证了元数据的完整性和可靠性，又避免了直接在resources目录中收集可能带来的风险。
