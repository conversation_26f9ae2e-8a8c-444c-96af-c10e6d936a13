@echo off
echo.
echo === Simple Metadata Checker ===
echo.

set "NEW_DIR=metadata\META-INF\native-image"
set "OLD_DIR=src\main\resources\META-INF\native-image"

echo Checking directories...
echo New metadata: %NEW_DIR%
echo Old metadata: %OLD_DIR%
echo.

if exist "%NEW_DIR%" (
    echo [OK] New metadata directory found
    echo Files in new metadata:
    dir "%NEW_DIR%\*.json" /b 2>nul
    if errorlevel 1 (
        echo   No JSON files found
    )
) else (
    echo [ERROR] New metadata directory not found
    echo Please run your program with JVM parameter:
    echo -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image
)

echo.

if exist "%OLD_DIR%" (
    echo [OK] Old metadata directory found
    echo Files in old metadata:
    dir "%OLD_DIR%\*.json" /b 2>nul
    if errorlevel 1 (
        echo   No JSON files found
    )
) else (
    echo [INFO] No old metadata (first time)
)

echo.
echo === Summary ===

if exist "%NEW_DIR%" (
    if exist "%OLD_DIR%" (
        echo You can compare and update metadata
        echo.
        echo Copy new metadata to resources? (y/n):
        set /p choice=
        if /i "%choice%"=="y" (
            echo Copying...
            if not exist "%OLD_DIR%" mkdir "%OLD_DIR%"
            copy "%NEW_DIR%\*.json" "%OLD_DIR%\" >nul 2>&1
            if errorlevel 0 (
                echo [OK] Copy completed
            ) else (
                echo [ERROR] Copy failed
            )
        )
    ) else (
        echo This is first time collection
        echo.
        echo Copy metadata to resources? (y/n):
        set /p choice=
        if /i "%choice%"=="y" (
            echo Copying...
            mkdir "%OLD_DIR%" 2>nul
            copy "%NEW_DIR%\*.json" "%OLD_DIR%\" >nul 2>&1
            if errorlevel 0 (
                echo [OK] Copy completed
            ) else (
                echo [ERROR] Copy failed
            )
        )
    )
) else (
    echo Please collect metadata first
)

echo.
pause
