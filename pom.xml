<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 目的组织标识符 -->
    <groupId>chms.server</groupId>
    <!-- 项目的唯一标识符 -->
    <artifactId>Server</artifactId>
    <!-- 项目的版本号 -->
    <version>1.0.0</version>
    <!-- 项目的打包类型，这里设置为 jar -->
    <packaging>jar</packaging>

    <properties>
        <!-- 报告输出的编码格式 -->
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 源代码文件的编码格式 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 项目使用的 Java 版本 -->
        <java.version>21</java.version>
        <!-- 编译器源代码版本 -->
        <maven.compiler.source>21</maven.compiler.source>
        <!-- 编译器目标代码版本 -->
        <maven.compiler.target>21</maven.compiler.target>
        <!-- 构建输出的最终 JAR 文件名称 -->
        <build.name>Server</build.name>

        <compiler.dir>${project.build.directory}/lib</compiler.dir>

        <!-- 元数据验证控制 -->
        <skipMetadataValidation>false</skipMetadataValidation>

        <!-- 系统相关的脚本配置 -->
        <metadata.script.executable>bash</metadata.script.executable>
        <metadata.script.name>validate-metadata.sh</metadata.script.name>

        <!--
             BRIEF: 输出简要信息，只显示最重要的警告和错误。 适合快速查看关键问题。
             DEFAULT: 输出默认详细程度的信息。 适合大多数情况，提供了合理的详细程度。
             VERBOSE: 输出详细信息，包括所有警告和错误。 适合需要详细了解所有潜在问题的情况。
        -->
        <maven.plugin.validation>VERBOSE</maven.plugin.validation>
    </properties>


    <dependencies>
        <!--
            https://mvnrepository.com/artifact/commons-io/commons-io
            主要功能：
            1: 提供强大的文件操作工具类（FileUtils），支持文件复制、移动、删除、比较等
            2: 实现高效的 I/O 流处理工具（IOUtils），简化流操作和资源关闭
            3: 包含文件名和路径处理工具（FilenameUtils，PathUtils）
            4: 支持文件内容比较（FileComparator）和监控（FileAlterationMonitor）
            5: 提供文件过滤器（FileFilter）和文件结束符处理工具（LineIterator）
            依赖关系：
            1: 纯 Java 实现，无外部依赖
            2: 需要 Java 7+ 运行环境（2.14.0 支持 Java 8+）
            3: 与 Apache Commons Lang 组合使用效果更佳
        -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.19.0</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/com.fasterxml.jackson.core/jackson-databind
            主要功能：
            1: 将 Java 对象序列化为 JSON 字符串（writeValueAsString()）
            2: 将 JSON 字符串反序列化为 Java 对象（readValue()）
            3: 支持泛型、集合类型（如 List<T>、Map<K,V>）等复杂结构的转换
            依赖关系：
            1: 自动依赖 jackson-core 和 jackson-annotations，所以只引入 jackson-databind，其他两个也会自动引入。
            2: 但如果显式指定了版本，则会使用指定的版本。
        -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.17.1</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/redis.clients/jedis
            主要功能：
            1: 提供完整的 Redis 客户端实现，支持所有 Redis 命令和数据类型（String/Hash/List/Set/SortedSet）
            2: 支持连接池管理（JedisPool），高效复用 Redis 连接
            3: 实现 Redis 集群操作（JedisCluster）、哨兵模式（JedisSentinel）和分片（Sharding）
            4: 支持事务（Transaction）、管道（Pipeline）、发布订阅（Pub/Sub）等高级特性
            5: 提供 Lua 脚本执行（eval/evalsha）和服务器管理命令（info/config）
            依赖关系：
            1: 核心依赖 commons-pool2（2.6.2+）用于连接池管理
            2: 可选依赖 slf4j-api（用于日志记录）
            3: 需要 Java 8+ 运行环境（6.x 版本要求 JDK 1.8+）
            4: 兼容 Redis 2.6.x 到 7.x 版本（推荐 Redis 5.0+）
        -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>6.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>json</artifactId>
                    <groupId>org.json</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/net.java.dev.jna/jna
            主要功能：
            1: 提供Java访问本地共享库（DLL/SO）的能力，无需编写JNI代码
            2: 支持直接调用C/C++函数、访问原生结构体和联合体
            3: 实现Java与本地代码之间的数据类型自动映射（如jint → int）
            4: 支持回调机制（从本地代码调用Java方法）
            5: 提供平台无关的本地库加载（Windows/Linux/macOS）
            依赖关系：
            1: 核心库无外部依赖（纯Java实现）
            2: 需要目标平台的本地库（通过jna-platform提供预绑定）
            3: 支持Java 8+（5.x版本要求Java 8+）
            4: 编译/测试/运行时均需要（默认compile scope）
        -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.17.0</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/net.java.dev.jna/jna-platform
            主要功能：
            1: 提供预绑定的常用操作系统本地库接口（Windows/Linux/macOS）
            2: 包含标准系统API的Java映射（如Windows API、POSIX函数、MacOS Carbon/Cocoa）
            3: 实现平台特定功能的跨平台访问（如注册表、系统信息、窗口管理）
            4: 提供常用系统库的常量定义和结构体映射
            5: 简化跨平台本地代码交互的开发工作
            依赖关系：
            1: 必须依赖 jna 核心库（相同版本）
            2: 自动包含对应平台的本地库头文件信息
            3: 编译/运行时均需要（默认compile scope）
            4: 支持Java 8+（与核心库要求一致）
        -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.17.0</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/ch.qos.logback/logback-classic
            主要功能：
            1: 提供完整的 SLF4J 实现，作为日志系统的核心组件
            2: 支持多级别日志输出（TRACE/DEBUG/INFO/WARN/ERROR）
            3: 实现灵活的日志配置（通过 XML 或 Groovy 配置文件）
            4: 提供强大的日志过滤、归档和滚动策略（如基于时间/大小的滚动）
            5: 支持异步日志记录提高性能，避免I/O阻塞
            依赖关系：
            1: 自动依赖 logback-core 和 slf4j-api
            2: 需要 Java 5+ 运行环境（1.5.18 支持 Java 8+）
            3: 与 logback-access（HTTP访问日志）和 logback-extras（扩展功能）兼容
            4: 编译期依赖（compile scope），默认包含在运行时
        -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.5.18</version>
            <!-- 排除内置的旧版本 org.slf4j-->
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/io.netty/netty-all
            主要功能：
            1: 提供异步事件驱动的网络应用框架，用于开发高性能网络服务器和客户端
            2: 支持多种传输协议（TCP/UDP/HTTP/WebSocket等）和编解码器
            3: 实现零拷贝技术减少内存复制，提升I/O性能
            4: 内置SSL/TLS支持实现安全通信
            5: 提供高度可定制的线程模型（单线程/多线程/主从多线程）
            依赖关系：
            1: 聚合了Netty所有核心模块（core/transport/codec/handler等）
            2: 使用此依赖后无需单独引入其他Netty子模块
            3: 注意：大型项目建议按需引入具体子模块以减少包大小
        -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <!-- GraaIvm兼容性最佳的版本 -->
            <version>4.1.109.Final</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/mysql/mysql-connector-java
            主要功能：
            1: 提供标准的 JDBC 驱动程序实现，用于 Java 应用程序连接 MySQL 数据库
            2: 支持执行 SQL 查询、更新操作以及存储过程调用
            3: 实现数据库连接池管理，支持连接复用
            4: 提供 SSL/TLS 加密连接支持，保障数据传输安全
            5: 支持 MySQL 特有的功能如 LOAD DATA LOCAL INFILE 和服务器端预处理语句
            依赖关系：
            1: 需要兼容的 MySQL 服务器版本（5.1.49 支持 MySQL 5.6/5.7）
            2: 运行时依赖 SLF4J 日志接口（需自行绑定日志实现如 Logback/Log4j2）
            3: Java 6+ 运行环境（建议使用 8.0+ 版本以获得更好性能）
        -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.49</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/commons-pool/commons-pool
            主要功能：
            1: 提供通用对象池实现，用于管理可复用对象（如数据库连接、线程等）的生命周期
            2: 支持对象借出（borrowObject）和归还（returnObject）的标准池操作
            3: 实现对象池的配置管理（最大空闲数、最小空闲数、最大总数等）
            4: 提供对象驱逐（eviction）机制自动清理空闲超时对象
            5: 支持JMX监控和池状态统计
            依赖关系：
            1: 纯Java实现，无强制外部依赖
            2: 常用作基础库被其他连接池实现（如DBCP、Redis客户端）依赖
            3: 需要Java 5+运行环境
        -->
        <dependency>
            <groupId>commons-pool</groupId>
            <artifactId>commons-pool</artifactId>
            <version>1.5.7</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/commons-dbcp/commons-dbcp
            主要功能：
            1: 提供基于 JDBC 的数据库连接池实现，管理数据库连接的创建、复用和销毁
            2: 支持连接泄漏检测和回收机制，防止连接未关闭导致资源耗尽
            3: 实现连接有效性验证（通过 testOnBorrow/testWhileIdle 等参数）
            4: 支持最大活动连接数、最大空闲连接数、最小空闲连接数等配置
            5: 提供基本的 JMX 监控支持，可查看连接池状态
            依赖关系：
            1: 必须依赖 commons-pool 对象池基础库（版本需兼容）
            2: 需要 JDBC 驱动程序（如 mysql-connector-java）
            3: 支持 Java 1.4+（建议使用 Java 8+）
        -->
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
            <!-- 排除内置的旧版本 pool -->
            <exclusions>
                <exclusion>
                    <groupId>commons-pool</groupId>
                    <artifactId>commons-pool</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/junit/junit
            主要功能：
            1: 提供 Java 单元测试框架，支持通过 @Test 注解定义测试方法
            2: 包含断言工具（如 assertEquals、assertTrue）验证代码逻辑正确性
            3: 支持测试生命周期管理（@Before/@After 初始化清理，@BeforeClass/@AfterClass 全局控制）
            4: 兼容参数化测试（@Parameterized）和测试套件（@Suite）
            依赖关系：
            1: 作用域为 test，仅在测试阶段有效（不参与编译和打包）
            2: 需要 Java 5+ 运行环境（4.12 支持 Java 8）
            3: 若需扩展功能（如 Mockito）需额外引入依赖
        -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.json/json
            主要功能：
            1: 提供轻量级 JSON 解析与生成能力（JSONObject/JSONArray 类）
            2: 支持 JSON 字符串与 Java 对象的相互转换（toMap()/put() 方法）
            3: 实现 JSON 数据格式校验（JSONStringer 生成标准格式）
            4: 可直接处理 HTTP 响应中的 JSON 数据（如 REST API 解析）
            依赖关系：
            1: 纯 Java 实现，无外部依赖（独立运行）
            2: 编译/运行时均有效（默认 compile 作用域）
            3: 兼容 Java 7+（20160810 版本支持 Java 8）
        -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20250517</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/com.alibaba/druid
            主要功能：
            1: 提供高性能的 JDBC 连接池实现，支持连接复用和泄漏检测
            2: 内置强大的 SQL 监控和防火墙功能（防 SQL 注入）
            3: 实现详细的运行统计（连接请求数、执行时间、错误数等）
            4: 支持配置加密（数据库密码保护）
            5: 提供可视化监控页面（需集成 druid-stat）
            依赖关系：
            1: 核心依赖 slf4j-api 进行日志记录
            2: 排除 jconsole/tools 依赖（减少体积，避免冲突）
            3: 兼容主流数据库（MySQL/Oracle/PostgreSQL 等）
            4: 需要 Java 8+ 环境（1.2.x 支持 Java 8）
        -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.2.25</version>
            <exclusions>
                <!-- 排除非必要工具包（约减少 2MB 体积） -->
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>jconsole</artifactId>
                </exclusion>
                <!-- 排除旧版工具库（可能与其他 Alibaba 组件冲突） -->
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>tools</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.projectlombok/lombok
            主要功能：
            1: 通过注解自动生成 Java Bean 方法（@Getter/@Setter）
            2: 简化日志声明（@Slf4j → 自动生成 log 变量）
            3: 自动生成 Builder 模式（@Builder）
            4: 消除空指针检查代码（@NonNull）
            5: 自动生成全参/无参构造器（@AllArgsConstructor/@NoArgsConstructor）
            依赖关系：
            1: provided 作用域表示仅编译期需要，运行时不需要
            2: 需 IDE 安装 Lombok 插件才能识别注解
            3: Java 编译时注解处理器（javac 或 ECJ）
        -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.38</version>
            <!-- 不打包到最终产物 -->
            <scope>provided</scope>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.jetbrains/annotations
            主要功能：
            1: 提供代码静态分析注解（@NotNull/@Nullable）
            2: 增强 IDE 的代码检查能力（空指针警告/类型检查）
            3: 支持方法契约注解（@Contract → 返回值与参数关系）
            4: 提供资源管理注解（@Nls → 国际化资源标记）
            依赖关系：
            1: 纯编译期注解，无运行时依赖
            2: 需配合 IntelliJ IDEA 或 FindBugs 等工具生效
            3: 兼容 Java 5+（13.0 支持 Java 8+）
        -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>26.0.2</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.graalvm.polyglot/polyglot
            主要功能：
            1: 提供多语言运行引擎（JavaScript/Python/Ruby/R 等）
            2: 支持在 JVM 中直接执行其他语言的代码
            3: 实现语言间的互操作（Java 与脚本语言互相调用）
            4: 提供高性能的脚本执行（基于 GraalVM 即时编译）
            依赖关系：
            1: 需要 GraalVM 运行时环境（或添加 truffle-api）
            2: 可选依赖语言实现（js/python/ruby 等）
            3: 需要 Java 11+（24.1.2 要求 Java 17+）
        -->
        <dependency>
            <groupId>org.graalvm.polyglot</groupId>
            <artifactId>polyglot</artifactId>
            <version>24.1.2</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.graalvm.truffle/truffle-api
            主要功能：
            1: 提供构建语言解释器的框架（实现自定义编程语言）
            2: 支持动态语言的即时编译（JIT）优化
            3: 实现跨语言互操作抽象层（Polyglot API 基础）
            4: 提供 AST 解释器构建工具和性能分析接口
            依赖关系：
            1: 核心依赖 GraalVM SDK（自动包含）
            2: 需要 Java 17+ 运行环境（24.1.2 要求 Java 17+）
            3: 与 polyglot 协同工作，但可独立使用
        -->
        <dependency>
            <groupId>org.graalvm.truffle</groupId>
            <artifactId>truffle-api</artifactId>
            <version>24.1.2</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.graalvm.js/js-scriptengine
            主要功能：
            1: 提供 JSR-223 兼容的 JavaScript 脚本引擎实现
            2: 支持传统 ScriptEngine API（javax.script）
            3: 桥接 Nashorn 迁移项目（兼容 Nashorn 脚本）
            4: 实现 JavaScript 与 Java 的互操作
            依赖关系：
            1: 排除 graal-sdk（避免与主 polyglot 冲突）
            2: 需要 JavaScript 引擎（js artifact）
            3: 兼容 Java 11+（建议 Java 17+）
        -->
        <dependency>
            <groupId>org.graalvm.js</groupId>
            <artifactId>js-scriptengine</artifactId>
            <version>24.1.2</version>
            <exclusions>
                <!-- 排除 SDK 避免版本冲突 -->
                <exclusion>
                    <groupId>org.graalvm.sdk</groupId>
                    <artifactId>graal-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.graalvm.js/js
            主要功能：
            1: 提供完整的 JavaScript 运行时环境（ECMAScript 2023 兼容）
            2: 支持 Node.js API 子集（fs/path/http 等模块）
            3: 实现高性能 JavaScript 执行（基于 GraalVM JIT）
            4: 支持 NPM 模块（需配合 package.json）
            依赖关系：
            1: 作用域为 runtime（编译期不需要）
            2: 排除 graal-sdk（避免冲突）
            3: 需要 Truffle API 支持
            4: 兼容 Java 11+（23.0.6 支持 Java 11）
        -->
        <dependency>
            <groupId>org.graalvm.js</groupId>
            <artifactId>js</artifactId>
            <version>23.0.6</version>
<!--            <version>24.1.2</version>-->
<!--            <type>pom</type>-->
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.graalvm.sdk</groupId>
                    <artifactId>graal-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>truffle-api</artifactId>
                    <groupId>org.graalvm.truffle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/com.mageddo.nativeimage/reflection-config-generator
            主要功能：
            1: 自动生成 GraalVM 原生镜像构建所需的反射配置文件
            2: 在编译时扫描代码中的反射调用，避免运行时异常
            3: 支持动态代理、资源加载和 JNI 的配置生成
            4: 简化 Spring Boot、Hibernate 等框架的原生镜像构建
            依赖关系：
            1: 排除 jackson-databind（避免与项目中其他 Jackson 版本冲突）
            2: 需要 GraalVM SDK（通过 truffle-api/polyglot 引入）
            3: 需在编译阶段执行（配置 Maven 插件）
        -->
        <dependency>
            <groupId>com.mageddo.nativeimage</groupId>
            <artifactId>reflection-config-generator</artifactId>
            <version>2.4.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.bouncycastle/bcprov-jdk18on
            主要功能：
            1: 提供 JCE 提供者实现（支持 AES/RSA/ECDSA 等算法）
            2: 实现安全随机数生成（CSPRNG）
            3: 支持 PKCS#7/CMS 和 S/MIME 消息格式
            4: 提供 TLS/DTLS 安全协议实现
            依赖关系：
            1: 自包含 JCE 提供者（无需外部依赖）
            2: 需要 Java 8+（jdk18on 表示支持 Java 8-21）
            3: 需注册为安全提供者（Security.addProvider()）
        -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.81</version>
        </dependency>
        <!--
            https://mvnrepository.com/artifact/org.slf4j/slf4j-api
            主要功能：
            1: 提供日志门面（Facade）API，作为 Java 日志系统的抽象层
            2: 支持多种日志实现（如 Logback、Log4j2、JDK Logging）的统一调用接口
            3: 实现参数化日志（延迟字符串拼接）提升性能
            4: 支持 MDC（Mapped Diagnostic Context）实现日志上下文跟踪
            5: 提供标记（Marker）功能实现复杂日志过滤
            依赖关系：
            1: 此为纯 API 依赖，不包含具体实现（需配合 logback-classic 等实现库）
            2: 编译期和运行时均需要（默认 compile scope）
            3: 与具体日志实现库版本需兼容（2.0.x 需配合 Logback 1.5.x+）
            4: 兼容 Java 8+（2.0.12 支持 Java 11+）
        -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.12</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>chms</groupId>-->
<!--            <artifactId>weblaf-complete</artifactId>-->
<!--            <version>1.29</version>-->
<!--            <scope>system</scope> &lt;!&ndash; 依赖不在 Maven 的中央仓库中，而是存在于本地文件系统中 &ndash;&gt;-->
<!--            <systemPath>${pom.basedir}/lib/weblaf-complete-1.29.jar</systemPath>-->
<!--        </dependency>-->
    </dependencies>

    <!-- 添加依赖版本管理 -->
    <dependencyManagement>
        <dependencies>
            <!-- 统一 SLF4J 版本 -->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-bom</artifactId>
                <version>2.0.12</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 统一 Jackson 版本 -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>2.17.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <!-- 设置构建输出的最终 JAR/WAR 文件名称 -->
        <finalName>${build.name}</finalName>
        <!-- 指定源代码目录 -->
        <sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
        <resources>
            <resource>
                <!-- 配置资源文件的目录为 -->
                <directory>src/main/resources</directory>
                <!-- 启用过滤功能 -->
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--
                    https://maven.apache.org/surefire/maven-surefire-plugin/
                    主要功能：
                    1: 执行项目中的单元测试（JUnit/TestNG）
                    2: 生成测试报告（target/surefire-reports）
                    3: 支持并行测试执行（提高测试效率）
                    4: 提供测试过滤机制（按类名/方法名/分组运行）
                    5: 支持多种测试框架（通过 Provider 机制）
                    配置说明：
                    1: <skip>true</skip> - 完全跳过测试执行（编译阶段仍会编译测试代码）
                    2: <argLine>-Dfile.encoding=UTF-8</argLine> - 设置测试 JVM 的默认编码为 UTF-8
                    3: 其他重要参数：
                       - <forkCount> - 控制测试进程数（默认 1）
                       - <includes> - 指定要运行的测试（如 **/*Test.java）
                       - <excludes> - 排除特定测试
                    注意事项：
                    1: 跳过测试会同时跳过单元测试和集成测试
                    2: 建议在 CI/CD 中保持 skip=false，仅在本地开发时临时跳过
                    3: UTF-8 编码可避免中文字符乱码导致的测试失败
                -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
                <configuration>
                    <!-- 跳过测试 -->
                    <skip>true</skip>
                    <!-- 传递给测试 JVM 的参数 -->
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                </configuration>
            </plugin>

            <!-- 用于打包 JAR 文件，配置了 MANIFEST 文件，添加类路径前缀 lib/ 并指定主类为 launch.StartServer。-->
            <plugin>
                <!--
                    https://maven.apache.org/plugins/maven-jar-plugin/
                    主要功能：
                    1: 将项目编译后的类文件和资源打包为 JAR 文件
                    2: 生成并配置 MANIFEST.MF 文件（包含元数据信息）
                    3: 支持自定义类路径（Class-Path）配置
                    4: 提供 JAR 文件的签名功能（需配合 jarsigner）
                    配置说明：
                    1: <addMavenDescriptor>false</addMavenDescriptor> - 不在 JAR 中包含 Maven 描述文件（pom.xml/pom.properties）
                    2: <addClasspath>true</addClasspath> - 在 MANIFEST 中添加 Class-Path 条目
                    3: <classpathPrefix>lib/</classpathPrefix> - 为依赖 JAR 添加路径前缀（表示依赖位于 lib/ 目录）
                    4: 默认输出位置：target/${project.build.finalName}.jar
                    注意事项：
                    1: 此插件仅打包项目自身代码，不包含依赖（需配合 maven-dependency-plugin 复制依赖）
                    2: Class-Path 条目需与依赖文件实际存放位置匹配
                    3: 主类配置（如需要）需额外添加 <mainClass>com.example.Main</mainClass>
                -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <archive>
                        <!-- 不包含 Maven 描述符（减少 JAR 体积约 1KB） -->
                        <addMavenDescriptor>false</addMavenDescriptor>
                        <manifest>
                            <!-- 启用类路径自动生成（必需功能） -->
                            <addClasspath>true</addClasspath>
                            <!--
                                 类路径前缀：所有依赖 JAR 将位于 lib/ 目录
                                 最终 MANIFEST 示例：Class-Path: lib/dependency1.jar lib/dependency2.jar
                            -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- 指定主类 -->
                            <mainClass>launch.StartServer</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>

            <!-- 在 package 阶段将依赖项复制到 ${project.build.directory}/lib 目录 -->
            <plugin>
                <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-dependency-plugin -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.6.1</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <!-- 在 package 生命周期阶段执行 -->
                        <phase>package</phase>
                        <goals>
                            <!-- 执行 copy-dependencies -->
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 指定输出目录，表示项目的构建输出目录，默认通常是 target 目录，表示编译输出目录） -->
                            <outputDirectory>${compiler.dir}</outputDirectory>
                            <!-- 覆盖已发布的依赖项 -->
                            <overWriteReleases>true</overWriteReleases>
                            <!-- 覆盖快照依赖项 -->
                            <overWriteSnapshots>true</overWriteSnapshots>
                            <!--- 如果目标文件较新，则覆盖 -->
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 配置资源文件的 -->
            <plugin>
                <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-resources-plugin -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <!-- 全局编码设置，影响所有资源文件 -->
                    <encoding>UTF-8</encoding>
                    <!-- .properties 文件的编码设置 -->
                    <propertiesEncoding>UTF-8</propertiesEncoding>
                </configuration>
                <executions>
                    <!-- 在native构建前复制元数据到resources -->
                    <execution>
                        <id>validate-and-copy-metadata</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/src/main/resources/META-INF/native-image</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${project.basedir}/metadata/META-INF/native-image</directory>
                                    <includes>
                                        <include>**/*.json</include>
                                    </includes>
                                    <!-- 如果源目录不存在则跳过 -->
                                    <optional>true</optional>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 元数据验证插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>validate-metadata</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <!-- Windows系统使用bat脚本，其他系统使用sh脚本 -->
                            <executable>${metadata.script.executable}</executable>
                            <arguments>
                                <argument>${project.basedir}/scripts/${metadata.script.name}</argument>
                                <argument>full</argument>
                            </arguments>
                            <!-- 如果脚本不存在则跳过 -->
                            <skip>${skipMetadataValidation}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 用于编译 Java 源代码，指定了源代码和目标代码的版本为 ${java.version}-->
            <plugin>
                <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-compiler-plugin -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <!--                <version>3.8.1</version>-->
                <version>3.13.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- Windows系统配置 -->
        <profile>
            <id>windows</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <properties>
                <metadata.script.executable>cmd</metadata.script.executable>
                <metadata.script.name>validate-metadata.bat</metadata.script.name>
            </properties>
        </profile>

        <profile>
            <id>native</id>
                        <build>
                            <plugins>
                                <plugin>
                                    <!-- https://mvnrepository.com/artifact/org.graalvm.buildtools/native-maven-plugin -->
                                    <groupId>org.graalvm.buildtools</groupId>
                                    <artifactId>native-maven-plugin</artifactId>
                                    <version>0.10.2</version>
                                    <!-- 启用插件扩展，允许插件在 Maven 生命周期中更早地参与构建过程 -->
                                    <extensions>true</extensions>
                                    <executions>
                                        <!-- 编译原生镜像 -->
                                        <execution>
                                            <id>build-native</id>
                                            <goals>
                                                <goal>compile-no-fork</goal>
                                            </goals>
                                            <phase>package</phase>
                                        </execution>
                                    </executions>
                                    <configuration>
                                        <!-- imageName用于设置生成的二进制文件名称 -->
                                        <imageName>${build.name}</imageName>
                                        <!-- mainClass用于指定main方法类路径 -->
                                        <mainClass>launch.StartServer</mainClass>
                                        <!-- native image 编译参数文档：https://docs.oracle.com/en/graalvm/enterprise/20/docs/reference-manual/native-image/NativeImageMavenPlugin/ -->
                                        <buildArgs>
                                            <buildArg>-Djava.util.concurrent.ForkJoinPool.common.parallelism=18</buildArg>
                                            <buildArg>-march=x86-64-v3</buildArg>                                           <!-- 指定目标架构为 x86-64-v3，确保生成的原生镜像能够在支持该架构的机器上运行 -->
                                            <buildArg>--initialize-at-build-time=org.slf4j,ch.qos.logback</buildArg>        <!-- 指定在构建时初始化包中的类，以确保这些类在运行时能够正常工作 -->
                                            <buildArg>--initialize-at-run-time=io.netty</buildArg>                          <!-- 指定在构建时初始化包中的类，以确保这些类在运行时能够正常工作 -->
                                            <buildArg>--strict-image-heap</buildArg>                                        <!-- 启用严格的镜像堆模式，确保生成的原生镜像在内存使用上更加严格 -->
                                            <buildArg>--no-fallback</buildArg>                                              <!-- 生产环境构建，确保镜像完全脱离JVM依赖，避免运行时性能损耗 -->
                                            <buildArg>--enable-url-protocols=http,https</buildArg>                          <!-- 仅启用应用实际需要的URL协议（如HTTP/HTTPS），减少不必要的资源加载 -->
                                            <buildArg>--verbose</buildArg>                                                  <!-- 输出详细信息 -->

                                            <buildArg>-H:+UnlockExperimentalVMOptions</buildArg>                            <!-- 解锁实验性的 VM 选项，允许使用一些实验性的功能 -->
                                            <buildArg>-H:Class=launch.StartServer</buildArg>                                <!-- 应用程序的入口点 -->
                                            <buildArg>-H:Name=${build.name}</buildArg>                                      <!-- 设置生成的原生镜像的名称，${build.name} 是一个 Maven 属性，表示构建的名称 -->
                                            <buildArg>-H:+AddAllCharsets</buildArg>                                         <!-- 添加所有字符集支持，确保生成的原生镜像能够处理各种字符集 -->
                                            <buildArg>-H:Path=D:/funmsServer/bin</buildArg>                                 <!-- 指定生成的原生镜像的输出路径 -->
                                            <buildArg>-H:NumberOfThreads=18</buildArg>                                      <!-- 提升至逻辑核心数75% (24*0.75=18) -->
                                            <buildArg>-H:Optimize="3"</buildArg>                                            <!-- 最高优化级别 -->
                                            <buildArg>-H:MicroArchitecture=x86-64-v3</buildArg>
                                            <buildArg>-H:InitialCollectionPolicy=BySpaceAndTime</buildArg>                  <!-- 优化 Serial GC 回收策略 -->
                                            <buildArg>-H:+DumpThreadStacksOnSignal</buildArg>                               <!-- 捕获GC时的线程状态 -->
                                            <buildArg>-H:+IncludeDebugHelperMethods</buildArg>                              <!-- 启用包含调试辅助方法的选项。 -->
                                            <buildArg>-H:+LogVerbose</buildArg>                                             <!-- 构建过程中启用详细的日志输出功能，帮助开发者调试和分析构建过程。 -->
                                            <buildArg>-H:+BuildReport</buildArg>                                            <!-- 生成原生镜像时输出详细的构建报告日志。-->
                                            <buildArg>-H:+AllowVMInspection</buildArg>                                      <!-- 启用 GC 日志的底层支持。-->

                                            <buildArg>-R:MaximumHeapSizePercent=70</buildArg>                               <!-- 最大堆占物理内存百分比 [保留30%内存给系统/其他进程, 相比默认80%更保守，预防内存争用]-->
                                            <buildArg>-R:MaximumYoungGenerationSizePercent=40</buildArg>                    <!-- 年轻代占堆百分比 [减少Young GC频率，适合短生命周期对象较多的场景]-->
                                            <buildArg>-R:MinHeapSize=12G</buildArg>                                         <!-- 堆内存分配 [固定堆避免扩容抖动]-->
                                            <buildArg>-R:MaxHeapSize=24G</buildArg>                                         <!-- 堆内存分配 [固定堆避免扩容抖动]-->
                                            <buildArg>-R:PercentTimeInIncrementalCollection=60</buildArg>                   <!-- 堆年轻代GC时间占比百分比 [提升年轻代GC时间权重，更积极回收临时对象]-->
                                            <buildArg>-R:StackSize=4m</buildArg>                                            <!-- 每个线程的栈大小 [栈空间预防深度递归导致的栈溢出]-->
                                            <buildArg>-R:MaxNewSize=2G</buildArg>                                           <!-- 增大年轻代空间，减少短生命周期对象晋升到老年代的频率，降低Full GC压力 -->
                                            <buildArg>-R:PercentTimeInIncrementalCollection=70</buildArg>                   <!-- 堆年轻代GC时间占比百分比 [提升年轻代GC时间权重，更积极回收临时对象]-->

            <!--                                <buildArg>-R:+LogVerbose</buildArg>-->
            <!--                                <buildArg>-R:+PrintGCSummary</buildArg>                                         &lt;!&ndash; 启用GC总结信息的打印。  &ndash;&gt;-->
            <!--                                <buildArg>-R:+PrintGC</buildArg>                                                &lt;!&ndash; 打印每次 GC 的基本信息。 &ndash;&gt;-->
            <!--                                <buildArg>-R:+VerboseGC</buildArg>                                              &lt;!&ndash; 启用基础 GC 日志。 &ndash;&gt;-->
                                            <!--                                <buildArg>-R:+PrintGCTimes</buildArg>-->
            <!--                                <buildArg>-R:LogFile=./logs/gc.log</buildArg>                                   &lt;!&ndash; 输出运行原生镜像的Log。 。 &ndash;&gt;-->
                                        </buildArgs>
                                    </configuration>
                                </plugin>
                            </plugins>
                        </build>
        </profile>
    </profiles>
</project>