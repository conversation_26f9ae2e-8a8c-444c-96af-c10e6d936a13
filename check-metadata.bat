@echo off
echo.
echo [INFO] Checking GraalVM metadata...
echo [DEBUG] Current directory: %CD%
echo.

REM Check if compare tool exists
if exist "scripts\compare-metadata.bat" (
    echo [DEBUG] Found compare tool, running...
    call scripts\compare-metadata.bat
) else (
    echo [ERROR] Compare tool not found: scripts\compare-metadata.bat
    echo [DEBUG] Directory contents:
    dir scripts
    pause
    exit /b 1
)

echo.
echo [INFO] Check completed!
pause
