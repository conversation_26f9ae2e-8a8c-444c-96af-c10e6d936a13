@echo off
echo.
echo ========================================
echo   GraalVM 元数据管理工具 - 增强版
echo ========================================
echo.

echo [1/3] 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java未安装或未配置到PATH
    echo 请安装Java 8+并配置环境变量
    pause
    exit /b 1
)
echo ✅ Java环境正常

echo.
echo [2/3] 编译GUI工具...
if not exist "src\main\java\tools\MetadataChecker.class" (
    echo 正在编译...
    javac -cp . src/main/java/tools/MetadataChecker.java
    
    if %errorlevel% neq 0 (
        echo ❌ 编译失败
        pause
        exit /b 1
    )
    echo ✅ 编译成功
) else (
    echo ✅ 已编译
)

echo.
echo [3/3] 启动GUI工具...
echo.
echo 🚀 正在启动增强版元数据管理工具...
echo.
echo 💡 新功能提示:
echo    - 支持自定义路径配置
echo    - 支持拖拽文件夹
echo    - 配置自动保存
echo    - 美化的界面设计
echo.

java -cp src/main/java tools.MetadataChecker

echo.
echo 工具已关闭
pause
