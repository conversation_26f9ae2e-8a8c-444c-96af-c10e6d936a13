package tools;

import java.io.File;

public class TestJsonNormalization {
    public static void main(String[] args) {
        try {
            MetadataChecker checker = new MetadataChecker();
            
            File original = new File("test-original.json");
            File reordered = new File("test-reordered.json");
            
            System.out.println("测试JSON位置变化检测:");
            System.out.println("原始文件: " + original.getName());
            System.out.println("重排序文件: " + reordered.getName());
            System.out.println();
            
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method compareMethod = MetadataChecker.class.getDeclaredMethod(
                "compareFileContent", File.class, File.class, String.class);
            compareMethod.setAccessible(true);
            
            Object result = compareMethod.invoke(checker, reordered, original, "test.json");
            
            System.out.println("对比结果: " + result);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
