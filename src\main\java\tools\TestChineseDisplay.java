package tools;

import javax.swing.*;
import java.awt.*;

public class TestChineseDisplay extends JFrame {
    
    public TestChineseDisplay() {
        setTitle("中文显示测试");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(400, 300);
        setLocationRelativeTo(null);
        
        JPanel panel = new JPanel(new BorderLayout());
        
        JTextArea textArea = new JTextArea();
        textArea.setFont(new Font("宋体", Font.PLAIN, 14));
        textArea.setText("这是中文测试\n" +
                        "测试特殊字符: ✅ ❌ 📁 📋\n" +
                        "测试普通中文: 文件 目录 检查 对比\n" +
                        "English test: OK ERROR INFO\n" +
                        "Mixed: [正常] [错误] [信息]");
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        JButton button = new JButton("测试按钮");
        button.addActionListener(e -> {
            textArea.append("\n点击了按钮: " + System.currentTimeMillis());
        });
        
        panel.add(button, BorderLayout.SOUTH);
        
        add(panel);
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                // 设置系统外观
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                
                // 设置字体
                Font font = new Font("微软雅黑", Font.PLAIN, 12);
                UIManager.put("Label.font", font);
                UIManager.put("Button.font", font);
                UIManager.put("TextArea.font", new Font("Consolas", Font.PLAIN, 12));
                
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            new TestChineseDisplay().setVisible(true);
        });
    }
}
