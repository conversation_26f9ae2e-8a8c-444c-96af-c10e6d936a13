# GraalVM Metadata Checker (PowerShell Version)
# Usage: Right-click -> Run with PowerShell

Write-Host ""
Write-Host "=== GraalVM Metadata Checker ===" -ForegroundColor Green
Write-Host ""

$newDir = "metadata\META-INF\native-image"
$oldDir = "src\main\resources\META-INF\native-image"

Write-Host "Checking directories..." -ForegroundColor Yellow
Write-Host "New metadata: $newDir"
Write-Host "Old metadata: $oldDir"
Write-Host ""

# Check new metadata directory
if (Test-Path $newDir) {
    Write-Host "[OK] New metadata directory found" -ForegroundColor Green
    $newFiles = Get-ChildItem "$newDir\*.json" -ErrorAction SilentlyContinue
    if ($newFiles) {
        Write-Host "Files in new metadata:"
        foreach ($file in $newFiles) {
            $size = [math]::Round($file.Length / 1KB, 2)
            Write-Host "  - $($file.Name) ($size KB)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  No JSON files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "[ERROR] New metadata directory not found" -ForegroundColor Red
    Write-Host "Please run your program with JVM parameter:" -ForegroundColor Yellow
    Write-Host "-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image" -ForegroundColor White
}

Write-Host ""

# Check old metadata directory
if (Test-Path $oldDir) {
    Write-Host "[OK] Old metadata directory found" -ForegroundColor Green
    $oldFiles = Get-ChildItem "$oldDir\*.json" -ErrorAction SilentlyContinue
    if ($oldFiles) {
        Write-Host "Files in old metadata:"
        foreach ($file in $oldFiles) {
            $size = [math]::Round($file.Length / 1KB, 2)
            Write-Host "  - $($file.Name) ($size KB)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  No JSON files found" -ForegroundColor Yellow
    }
} else {
    Write-Host "[INFO] No old metadata (first time collection)" -ForegroundColor Blue
}

Write-Host ""
Write-Host "=== Analysis ===" -ForegroundColor Green

if (Test-Path $newDir) {
    $newFiles = Get-ChildItem "$newDir\*.json" -ErrorAction SilentlyContinue
    
    if ($newFiles) {
        Write-Host "Checking JSON file integrity..." -ForegroundColor Yellow
        
        foreach ($file in $newFiles) {
            try {
                $content = Get-Content $file.FullName -Raw
                $json = ConvertFrom-Json $content -ErrorAction Stop
                Write-Host "  [OK] $($file.Name) - Valid JSON" -ForegroundColor Green
            } catch {
                Write-Host "  [ERROR] $($file.Name) - Invalid JSON!" -ForegroundColor Red
            }
        }
        
        Write-Host ""
        
        if (Test-Path $oldDir) {
            Write-Host "Comparing with old metadata..." -ForegroundColor Yellow
            $oldFiles = Get-ChildItem "$oldDir\*.json" -ErrorAction SilentlyContinue
            
            $differences = 0
            foreach ($newFile in $newFiles) {
                $oldFile = $oldFiles | Where-Object { $_.Name -eq $newFile.Name }
                if ($oldFile) {
                    if ((Get-FileHash $newFile.FullName).Hash -ne (Get-FileHash $oldFile.FullName).Hash) {
                        $sizeDiff = $newFile.Length - $oldFile.Length
                        Write-Host "  [DIFF] $($newFile.Name) - Changed (${sizeDiff} bytes difference)" -ForegroundColor Yellow
                        $differences++
                    } else {
                        Write-Host "  [SAME] $($newFile.Name) - Identical" -ForegroundColor Green
                    }
                } else {
                    Write-Host "  [NEW] $($newFile.Name) - New file" -ForegroundColor Blue
                    $differences++
                }
            }
            
            if ($differences -eq 0) {
                Write-Host ""
                Write-Host "All files are identical. No update needed." -ForegroundColor Green
            } else {
                Write-Host ""
                Write-Host "Found $differences file(s) with differences." -ForegroundColor Yellow
            }
        }
        
        Write-Host ""
        Write-Host "=== Action ===" -ForegroundColor Green
        
        if (Test-Path $oldDir) {
            $choice = Read-Host "Update metadata in resources directory? (y/n)"
        } else {
            $choice = Read-Host "Copy metadata to resources directory? (y/n)"
        }
        
        if ($choice -eq "y" -or $choice -eq "Y") {
            try {
                if (!(Test-Path $oldDir)) {
                    New-Item -ItemType Directory -Path $oldDir -Force | Out-Null
                }
                
                Copy-Item "$newDir\*.json" $oldDir -Force
                Write-Host "[SUCCESS] Metadata updated successfully!" -ForegroundColor Green
            } catch {
                Write-Host "[ERROR] Failed to copy metadata: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "[SKIP] Metadata not updated." -ForegroundColor Yellow
        }
    } else {
        Write-Host "No JSON files found in new metadata directory." -ForegroundColor Yellow
        Write-Host "Make sure to run your application to collect metadata." -ForegroundColor Yellow
    }
} else {
    Write-Host "Please collect metadata first by running your application with the agent parameter." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
