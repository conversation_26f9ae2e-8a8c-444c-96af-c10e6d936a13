# GraalVM 元数据管理工具 (Windows)

## 简单实用的解决方案

你说得对，复杂的自动化不如简单可控的手动操作。这个工具帮你：

1. **检查新收集的元数据是否完整**
2. **对比新旧元数据的差异**
3. **安全地复制到resources目录**

## 使用流程

### 1. 收集元数据
在IDEA中设置JVM参数：
```
-agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image
```

### 2. 检查和对比元数据
运行对比工具：
```cmd
scripts\compare-metadata.bat
```

### 3. 根据结果决定是否更新
工具会显示：
- ✅ 新元数据的完整性检查结果
- 📊 文件大小和条目数统计
- 🔍 与现有元数据的差异对比
- ❓ 是否要复制到resources目录

## 工具功能详解

### 完整性检查
- **JSON格式验证**：确保文件格式正确，没有被截断
- **内容分析**：显示每个文件的大小和大概条目数
- **警告提示**：如果文件很小或条目很少会给出提示

### 差异对比
- **文件对比**：逐个对比配置文件
- **变化标识**：
  - `=` 文件相同
  - `△` 文件有变化（显示大小差异）
  - `+` 新增文件
  - `-` 删除文件

### 统计信息
- 新旧元数据的文件数量
- 总文件大小对比
- 帮助判断元数据收集是否充分

## 使用示例

### 首次收集
```
=== GraalVM 元数据对比工具 ===

[信息] 新元数据目录: metadata\META-INF\native-image
[提示] resources目录中没有元数据，这是首次收集

=== 检查新元数据完整性 ===
检查目录: metadata\META-INF\native-image
  ✓ reflect-config.json - 正常 (2048 bytes, ~45 条目)
  ✓ resource-config.json - 正常 (1024 bytes, ~12 条目)
  ? proxy-config.json - 文件不存在（可能未使用相关功能）
  ✓ jni-config.json - 正常 (512 bytes, ~8 条目)
  ? serialization-config.json - 文件不存在（可能未使用相关功能）
✓ 元数据完整性检查通过

=== 总结 ===
📊 元数据统计:
  新元数据: 3 个文件, 总大小 3584 bytes
  旧元数据: 无 (首次收集)

是否要复制新元数据到resources目录？ (y/n): y
正在复制元数据...
复制成功
[完成] 元数据已更新到resources目录
```

### 更新检查
```
=== GraalVM 元数据对比工具 ===

=== 检查新元数据完整性 ===
✓ 元数据完整性检查通过

=== 对比元数据差异 ===
对比配置文件差异...
  = reflect-config.json - 文件相同
  △ resource-config.json - 文件有变化
    文件增大了 256 bytes
  + proxy-config.json - 新增文件
发现 2 个文件有差异

=== 总结 ===
📊 元数据统计:
  新元数据: 4 个文件, 总大小 4096 bytes
  旧元数据: 3 个文件, 总大小 3584 bytes

是否要复制新元数据到resources目录？ (y/n): y
```

## 什么时候需要更新？

### 建议更新的情况：
- ✅ 新增了配置文件（如proxy-config.json）
- ✅ 文件大小明显增加（说明收集到更多元数据）
- ✅ 添加了新功能后首次运行

### 可以不更新的情况：
- ⚪ 所有文件都相同
- ⚪ 文件大小变化很小（可能只是顺序变化）
- ⚪ 只是测试运行，没有使用新功能

## 注意事项

1. **运行充分的测试**：确保程序的各个功能都被执行到
2. **检查文件大小**：如果某个配置文件异常小，可能需要更多测试
3. **备份重要配置**：虽然工具很安全，但重要项目建议手动备份
4. **分阶段收集**：复杂应用建议分多次运行收集不同场景的元数据

## 目录结构
```
项目根目录/
├── metadata/                          # 新收集的元数据（临时）
│   └── META-INF/native-image/
│       ├── reflect-config.json
│       ├── resource-config.json
│       └── ...
├── src/main/resources/                # 用于构建的元数据（正式）
│   └── META-INF/native-image/
├── scripts/
│   └── compare-metadata.bat          # 对比工具
└── pom.xml
```

这样你就有了一个简单实用的工具，既能确保元数据完整性，又能清楚地看到变化，手动控制更新时机！
