package tools;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.TitledBorder;
import javax.swing.text.*;
import java.awt.*;
import java.awt.datatransfer.DataFlavor;
import java.awt.dnd.*;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.List;
import java.util.prefs.Preferences;

/**
 * GraalVM 元数据检查和管理工具 (增强版)
 * 支持自定义路径、拖拽文件、配置持久化和美化界面
 */
public class MetadataChecker extends JFrame {

    // 配置键名
    private static final String PREF_NEW_DIR = "new_metadata_dir";
    private static final String PREF_OLD_DIR = "old_metadata_dir";
    private static final String PREF_TARGET_DIR = "target_dir";

    // 默认路径
    private static final String DEFAULT_NEW_DIR = "metadata/META-INF/native-image";
    private static final String DEFAULT_OLD_DIR = "src/main/resources/META-INF/native-image";
    private static final String DEFAULT_TARGET_DIR = "src/main/resources/META-INF/native-image";

    // 配置存储
    private Preferences prefs;

    // 当前路径
    private String newMetadataDir;
    private String oldMetadataDir;
    private String targetDir;

    // UI组件
    private JTextPane logArea;
    private StyledDocument logDocument;

    // 日志颜色样式
    private SimpleAttributeSet normalStyle;
    private SimpleAttributeSet successStyle;
    private SimpleAttributeSet errorStyle;
    private SimpleAttributeSet warningStyle;
    private SimpleAttributeSet infoStyle;
    private SimpleAttributeSet changeStyle;     // 文件变化 - 紫色
    private SimpleAttributeSet addStyle;        // 新增内容 - 深绿色
    private SimpleAttributeSet deleteStyle;     // 删除内容 - 深红色
    private SimpleAttributeSet sizeStyle;       // 大小变化 - 橙红色
    private SimpleAttributeSet locationStyle;   // 位置信息 - 深蓝色
    private JButton checkButton;
    private JButton compareButton;
    private JButton copyButton;
    private JButton refreshButton;

    private JPanel newMetadataPanel;
    private JPanel oldMetadataPanel;
    private JTextField newDirField;
    private JTextField oldDirField;
    private JTextField targetDirField;
    
    public MetadataChecker() {
        // 初始化配置
        prefs = Preferences.userNodeForPackage(MetadataChecker.class);
        loadConfiguration();

        initializeGUI();
        refreshStatus();
    }

    private void loadConfiguration() {
        newMetadataDir = prefs.get(PREF_NEW_DIR, DEFAULT_NEW_DIR);
        oldMetadataDir = prefs.get(PREF_OLD_DIR, DEFAULT_OLD_DIR);
        targetDir = prefs.get(PREF_TARGET_DIR, DEFAULT_TARGET_DIR);
    }

    private void saveConfiguration() {
        prefs.put(PREF_NEW_DIR, newMetadataDir);
        prefs.put(PREF_OLD_DIR, oldMetadataDir);
        prefs.put(PREF_TARGET_DIR, targetDir);
    }

    private void initializeGUI() {
        setTitle("GraalVM 元数据管理工具 - 增强版");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());

        // 设置现代化外观
        setupModernLookAndFeel();

        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        mainPanel.setBorder(new EmptyBorder(10, 10, 10, 10));

        // 顶部路径配置面板
        JPanel pathConfigPanel = createPathConfigPanel();
        mainPanel.add(pathConfigPanel, BorderLayout.NORTH);

        // 中间内容面板
        JPanel contentPanel = new JPanel(new GridLayout(1, 2, 15, 0));
        contentPanel.setBorder(new EmptyBorder(10, 0, 10, 0));

        // 新元数据面板
        newMetadataPanel = createMetadataPanel("📁 新收集的元数据", newMetadataDir, true);
        contentPanel.add(newMetadataPanel);

        // 旧元数据面板
        oldMetadataPanel = createMetadataPanel("📂 对比元数据", oldMetadataDir, true);
        contentPanel.add(oldMetadataPanel);

        mainPanel.add(contentPanel, BorderLayout.CENTER);

        // 底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);

        // 日志面板
        JPanel logPanel = createLogPanel();

        // 使用分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, mainPanel, logPanel);
        splitPane.setDividerLocation(450);
        splitPane.setResizeWeight(0.75);
        splitPane.setBorder(null);

        add(splitPane);

        // 设置窗口属性
        setSize(1000, 750);
        setLocationRelativeTo(null);
        setMinimumSize(new Dimension(800, 600));
    }

    private void setupModernLookAndFeel() {
        try {
            // 设置系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());

            // 设置中文字体 - 关键修复
            Font chineseFont = new Font("微软雅黑", Font.PLAIN, 12);
            Font monoFont = new Font("Consolas", Font.PLAIN, 12);

            UIManager.put("Label.font", chineseFont);
            UIManager.put("Button.font", chineseFont);
            UIManager.put("TextArea.font", monoFont);
            UIManager.put("TextField.font", chineseFont);
            UIManager.put("TitledBorder.font", chineseFont.deriveFont(Font.BOLD));

        } catch (Exception e) {
            // 使用默认外观
        }
    }
    
    private JPanel createPathConfigPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder("📂 路径配置");
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 14f));
        panel.setBorder(border);
        panel.setBackground(new Color(248, 249, 250));

        JPanel configPanel = new JPanel(new GridBagLayout());
        configPanel.setBackground(new Color(248, 249, 250));
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;

        // 新元数据路径
        gbc.gridx = 0; gbc.gridy = 0;
        configPanel.add(new JLabel("📁 新元数据目录:"), gbc);

        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        newDirField = new JTextField(newMetadataDir, 30);
        setupDragAndDrop(newDirField, true);
        configPanel.add(newDirField, gbc);

        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton newDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        newDirButton.addActionListener(e -> selectDirectory(newDirField, "选择新元数据目录", true));
        configPanel.add(newDirButton, gbc);

        gbc.gridx = 3;
        JButton openNewDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openNewDirButton.addActionListener(e -> openDirectory(newDirField.getText()));
        configPanel.add(openNewDirButton, gbc);

        // 对比元数据路径
        gbc.gridx = 0; gbc.gridy = 1; gbc.weightx = 0;
        configPanel.add(new JLabel("📂 对比元数据目录:"), gbc);

        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        oldDirField = new JTextField(oldMetadataDir, 30);
        setupDragAndDrop(oldDirField, true);
        configPanel.add(oldDirField, gbc);

        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton oldDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        oldDirButton.addActionListener(e -> selectDirectory(oldDirField, "选择对比元数据目录", true));
        configPanel.add(oldDirButton, gbc);

        gbc.gridx = 3;
        JButton openOldDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openOldDirButton.addActionListener(e -> openDirectory(oldDirField.getText()));
        configPanel.add(openOldDirButton, gbc);

        // 目标路径
        gbc.gridx = 0; gbc.gridy = 2; gbc.weightx = 0;
        configPanel.add(new JLabel("🎯 复制目标目录:"), gbc);

        gbc.gridx = 1; gbc.weightx = 1.0; gbc.fill = GridBagConstraints.HORIZONTAL;
        targetDirField = new JTextField(targetDir, 30);
        setupDragAndDrop(targetDirField, true);
        configPanel.add(targetDirField, gbc);

        gbc.gridx = 2; gbc.weightx = 0; gbc.fill = GridBagConstraints.NONE;
        JButton targetDirButton = createStyledButton("浏览", new Color(52, 152, 219));
        targetDirButton.addActionListener(e -> selectDirectory(targetDirField, "选择复制目标目录", true));
        configPanel.add(targetDirButton, gbc);

        gbc.gridx = 3;
        JButton openTargetDirButton = createStyledButton("打开", new Color(46, 204, 113));
        openTargetDirButton.addActionListener(e -> openDirectory(targetDirField.getText()));
        configPanel.add(openTargetDirButton, gbc);

        panel.add(configPanel, BorderLayout.CENTER);

        // 添加路径变化监听器
        newDirField.addActionListener(e -> updatePaths());
        oldDirField.addActionListener(e -> updatePaths());
        targetDirField.addActionListener(e -> updatePaths());

        return panel;
    }

    private JPanel createMetadataPanel(String title, String directory, boolean supportDrop) {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder(title);
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 13f));
        panel.setBorder(border);
        panel.setBackground(Color.WHITE);

        JTextArea textArea = new JTextArea(12, 30);
        textArea.setEditable(false);
        // 设置支持中文的字体
        textArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        textArea.setBackground(new Color(253, 253, 253));
        textArea.setBorder(new EmptyBorder(8, 8, 8, 8));

        if (supportDrop) {
            setupDragAndDrop(textArea, false);
        }

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setBorder(BorderFactory.createLoweredBevelBorder());
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);

        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }
    
    private JButton createStyledButton(String text, Color color) {
        JButton button = new JButton(text);
        button.setBackground(color);
        button.setForeground(Color.WHITE);
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setFont(button.getFont().deriveFont(Font.BOLD, 12f));
        button.setPreferredSize(new Dimension(80, 30));
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));

        // 添加悬停效果
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(color.brighter());
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(color);
            }
        });

        return button;
    }

    private void setupDragAndDrop(JComponent component, boolean isDirectory) {
        component.setDropTarget(new DropTarget() {
            @Override
            public synchronized void drop(DropTargetDropEvent evt) {
                try {
                    evt.acceptDrop(DnDConstants.ACTION_COPY);
                    @SuppressWarnings("unchecked")
                    List<File> droppedFiles = (List<File>) evt.getTransferable()
                            .getTransferData(DataFlavor.javaFileListFlavor);

                    if (!droppedFiles.isEmpty()) {
                        File file = droppedFiles.get(0);
                        String path;

                        if (isDirectory) {
                            path = file.isDirectory() ? file.getAbsolutePath() : file.getParent();
                        } else {
                            path = file.getAbsolutePath();
                        }

                        if (component instanceof JTextField) {
                            ((JTextField) component).setText(path);
                            updatePaths();
                        }

                        log("📁 拖拽设置路径: " + path);
                    }
                } catch (Exception ex) {
                    log("❌ 拖拽操作失败: " + ex.getMessage());
                }
            }
        });
    }

    private void selectDirectory(JTextField field, String title, boolean createIfNotExists) {
        JFileChooser chooser = new JFileChooser();
        chooser.setDialogTitle(title);
        chooser.setFileSelectionMode(JFileChooser.DIRECTORIES_ONLY);
        chooser.setCurrentDirectory(new File(field.getText()));

        if (chooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
            String selectedPath = chooser.getSelectedFile().getAbsolutePath();
            field.setText(selectedPath);
            updatePaths();
            log("📂 选择路径: " + selectedPath);
        }
    }

    private void openDirectory(String path) {
        if (path == null || path.trim().isEmpty()) {
            log("❌ 路径为空，无法打开");
            return;
        }

        File dir = new File(path.trim());
        if (!dir.exists()) {
            log("❌ 目录不存在: " + path);
            return;
        }

        try {
            if (Desktop.isDesktopSupported()) {
                Desktop.getDesktop().open(dir);
                log("📂 已打开目录: " + path);
            } else {
                // Windows fallback
                Runtime.getRuntime().exec("explorer.exe \"" + dir.getAbsolutePath() + "\"");
                log("📂 已打开目录: " + path);
            }
        } catch (IOException e) {
            log("❌ 打开目录失败: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "无法打开目录: " + e.getMessage(),
                "错误",
                JOptionPane.ERROR_MESSAGE);
        }
    }

    private void updatePaths() {
        newMetadataDir = newDirField.getText().trim();
        oldMetadataDir = oldDirField.getText().trim();
        targetDir = targetDirField.getText().trim();

        saveConfiguration();
        refreshStatus();
    }

    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 15, 10));
        panel.setBackground(new Color(248, 249, 250));

        refreshButton = createStyledButton("🔄 刷新状态", new Color(46, 204, 113));
        checkButton = createStyledButton("🔍 检查完整性", new Color(52, 152, 219));
        compareButton = createStyledButton("📊 对比差异", new Color(155, 89, 182));
        copyButton = createStyledButton("📋 复制文件", new Color(231, 76, 60));

        // 设置按钮大小
        Dimension buttonSize = new Dimension(120, 35);
        refreshButton.setPreferredSize(buttonSize);
        checkButton.setPreferredSize(buttonSize);
        compareButton.setPreferredSize(buttonSize);
        copyButton.setPreferredSize(buttonSize);

        // 添加按钮事件
        refreshButton.addActionListener(e -> refreshStatus());
        checkButton.addActionListener(e -> checkIntegrity());
        compareButton.addActionListener(e -> compareMetadata());
        copyButton.addActionListener(e -> copyMetadata());

        panel.add(refreshButton);
        panel.add(checkButton);
        panel.add(compareButton);
        panel.add(copyButton);

        return panel;
    }
    
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        TitledBorder border = BorderFactory.createTitledBorder("📝 操作日志");
        border.setTitleFont(border.getTitleFont().deriveFont(Font.BOLD, 14f));
        panel.setBorder(border);

        logArea = new JTextPane();
        logArea.setEditable(false);
        // 设置支持中文的字体
        logArea.setFont(new Font("微软雅黑", Font.PLAIN, 11));
        logArea.setBackground(new Color(248, 249, 250));

        // 获取文档对象用于颜色设置
        logDocument = logArea.getStyledDocument();

        // 初始化颜色样式
        initializeLogStyles();

        // 设置首选大小
        logArea.setPreferredSize(new Dimension(800, 200));

        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);

        panel.add(scrollPane, BorderLayout.CENTER);

        // 按钮面板
        JPanel logButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));

        JButton clearLogButton = createStyledButton("清除日志", new Color(108, 117, 125));
        clearLogButton.addActionListener(e -> {
            logArea.setText("");
            log("📝 日志已清除");
        });

        JButton saveLogButton = createStyledButton("保存日志", new Color(40, 167, 69));
        saveLogButton.addActionListener(e -> saveLogToFile());

        logButtonPanel.add(saveLogButton);
        logButtonPanel.add(clearLogButton);
        panel.add(logButtonPanel, BorderLayout.SOUTH);

        return panel;
    }

    private void saveLogToFile() {
        try {
            String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String filename = "metadata_check_log_" + timestamp + ".txt";

            Files.write(Paths.get(filename), logArea.getText().getBytes());
            log("💾 日志已保存到: " + filename);

            JOptionPane.showMessageDialog(this,
                "日志已保存到: " + filename,
                "保存成功",
                JOptionPane.INFORMATION_MESSAGE);

        } catch (IOException e) {
            log("❌ 保存日志失败: " + e.getMessage());
            JOptionPane.showMessageDialog(this,
                "保存失败: " + e.getMessage(),
                "错误",
                JOptionPane.ERROR_MESSAGE);
        }
    }
    
    private void refreshStatus() {
        log("=== 刷新元数据状态 ===");

        // 更新新元数据面板
        updateMetadataPanel(newMetadataPanel, newMetadataDir, "📁 新收集的元数据");

        // 更新旧元数据面板
        updateMetadataPanel(oldMetadataPanel, oldMetadataDir, "📂 对比元数据");

        log("状态刷新完成\n");
    }
    
    private void updateMetadataPanel(JPanel panel, String directory, String title) {
        JTextArea textArea = getTextAreaFromPanel(panel);
        StringBuilder content = new StringBuilder();
        
        File dir = new File(directory);
        if (!dir.exists()) {
            content.append("📂 目录不存在: ").append(directory).append("\n\n");
            if (directory.equals(newMetadataDir)) {
                content.append("💡 请先运行程序收集元数据\n\n");
                content.append("🔧 JVM参数示例:\n");
                content.append("-agentlib:native-image-agent=\n");
                content.append("config-merge-dir=metadata/META-INF/native-image\n\n");
                content.append("📝 或者拖拽文件夹到路径配置区域");
            } else {
                content.append("ℹ️  可以选择或拖拽一个包含元数据的目录");
            }
        } else {
            File[] allFiles = dir.listFiles();
            if (allFiles == null || allFiles.length == 0) {
                content.append("📂 目录存在但为空\n");
            } else {
                // 递归显示所有文件和文件夹
                int[] dirCount_arr = {0};
                int[] fileCount_arr = {0};
                int[] jsonCount_arr = {0};
                long[] totalSize_arr = {0};

                displayDirectoryContents(content, dir, "", dirCount_arr, fileCount_arr, jsonCount_arr, totalSize_arr);

                // 统计信息
                content.append("\n=== 统计信息 ===\n");
                content.append(String.format("   文件夹: %d 个\n", dirCount_arr[0]));
                content.append(String.format("   文件: %d 个 (其中JSON: %d 个)\n", fileCount_arr[0], jsonCount_arr[0]));
                content.append(String.format("   总大小: %s", formatFileSize(totalSize_arr[0])));
            }
        }
        
        textArea.setText(content.toString());
    }

    private void displayDirectoryContents(StringBuilder content, File dir, String indent,
                                        int[] dirCount, int[] fileCount, int[] jsonCount, long[] totalSize) {
        File[] files = dir.listFiles();
        if (files == null) return;

        // 排序：文件夹在前，文件在后，按名称排序
        Arrays.sort(files, (a, b) -> {
            if (a.isDirectory() && !b.isDirectory()) return -1;
            if (!a.isDirectory() && b.isDirectory()) return 1;
            return a.getName().compareToIgnoreCase(b.getName());
        });

        for (File file : files) {
            if (file.isDirectory()) {
                dirCount[0]++;
                content.append(String.format("%s[文件夹] %s/\n", indent, file.getName()));

                // 递归显示子目录内容（限制深度避免过深）
                if (indent.length() < 6) { // 最多3层深度
                    displayDirectoryContents(content, file, indent + "  ", dirCount, fileCount, jsonCount, totalSize);
                }
            } else {
                fileCount[0]++;
                long size = file.length();
                totalSize[0] += size;

                String icon = "[文件]";
                if (file.getName().endsWith(".json")) {
                    icon = "[JSON]";
                    jsonCount[0]++;
                } else if (file.getName().endsWith(".properties")) {
                    icon = "[配置]";
                } else if (file.getName().endsWith(".txt")) {
                    icon = "[文本]";
                } else if (file.getName().endsWith(".class")) {
                    icon = "[类文件]";
                }

                content.append(String.format("%s%s %-30s %s\n",
                    indent, icon, file.getName(), formatFileSize(size)));
            }
        }
    }

    private JTextArea getTextAreaFromPanel(JPanel panel) {
        Component[] components = panel.getComponents();
        for (Component comp : components) {
            if (comp instanceof JScrollPane) {
                JScrollPane scrollPane = (JScrollPane) comp;
                return (JTextArea) scrollPane.getViewport().getView();
            }
        }
        return null;
    }
    
    private void checkIntegrity() {
        log("=== 检查元数据完整性 ===");
        log("📂 检查目录: " + newMetadataDir);
        log("");

        File newDir = new File(newMetadataDir);
        if (!newDir.exists()) {
            log("❌ 目录不存在，请先收集元数据");
            return;
        }

        String[] requiredFiles = {
            "reflect-config.json",
            "resource-config.json",
            "proxy-config.json",
            "jni-config.json",
            "serialization-config.json"
        };

        int issues = 0;
        for (String fileName : requiredFiles) {
            File file = new File(newDir, fileName);
            if (file.exists()) {
                if (isValidJson(file)) {
                    long size = file.length();
                    String relativePath = getRelativePath(file);
                    if (size < 10) {
                        log("[警告] " + fileName + " - 文件很小 (" + size + " bytes)");
                        log("   路径: " + relativePath);
                    } else {
                        log("[正常] " + fileName + " - 格式正确 (" + formatFileSize(size) + ")");
                        log("   路径: " + relativePath);
                    }
                } else {
                    log("[错误] " + fileName + " - JSON格式错误!");
                    log("   路径: " + getRelativePath(file));
                    issues++;
                }
            } else {
                log("[信息] " + fileName + " - 文件不存在 (可能未使用相关功能)");
            }
        }

        log("");
        if (issues == 0) {
            log("[成功] 元数据完整性检查通过!");
        } else {
            log("[错误] 发现 " + issues + " 个问题!");
        }
        log("");
    }

    private String getRelativePath(File file) {
        try {
            String currentDir = System.getProperty("user.dir");
            String filePath = file.getAbsolutePath();
            if (filePath.startsWith(currentDir)) {
                return filePath.substring(currentDir.length() + 1);
            }
            return filePath;
        } catch (Exception e) {
            return file.getAbsolutePath();
        }
    }







    private void compareMetadata() {
        log("=== 对比元数据差异 ===");

        File newDir = new File(newMetadataDir);
        File oldDir = new File(oldMetadataDir);

        if (!newDir.exists()) {
            log("❌ 新元数据目录不存在");
            return;
        }

        if (!oldDir.exists()) {
            log("[信息] 对比目录中没有元数据，这是首次收集");
            log("");
            return;
        }

        // 递归对比所有文件和文件夹
        int[] totalDifferences = {0};
        compareDirectoryRecursive(newDir, oldDir, "", totalDifferences);

        log("");
        if (totalDifferences[0] == 0) {
            log("[信息] 所有文件都相同，无需更新");
        } else {
            log("[信息] 发现 " + totalDifferences[0] + " 个文件有差异");
        }
        log("");
    }

    private void compareDirectoryRecursive(File newDir, File oldDir, String relativePath, int[] totalDifferences) {
        // 获取所有文件和文件夹进行对比
        Set<String> allNames = new HashSet<>();
        File[] newFiles = newDir.listFiles();
        File[] oldFiles = oldDir.listFiles();

        if (newFiles != null) {
            for (File file : newFiles) {
                allNames.add(file.getName());
            }
        }

        if (oldFiles != null) {
            for (File file : oldFiles) {
                allNames.add(file.getName());
            }
        }

        for (String name : allNames) {
            File newFile = new File(newDir, name);
            File oldFile = new File(oldDir, name);
            String currentPath = relativePath.isEmpty() ? name : relativePath + "/" + name;

            if (newFile.exists() && oldFile.exists()) {
                // 两边都存在
                if (newFile.isDirectory() && oldFile.isDirectory()) {
                    log("[文件夹] " + currentPath + "/ - 递归对比子目录");
                    // 递归对比子目录
                    compareDirectoryRecursive(newFile, oldFile, currentPath, totalDifferences);
                } else if (newFile.isFile() && oldFile.isFile()) {
                    // 智能对比文件内容
                    ContentComparisonResult result = compareFileContent(newFile, oldFile, name);

                    if (result.hasRealChanges) {
                        log("[变化] " + currentPath + " - " + result.changeDescription);
                        long newSize = newFile.length();
                        long oldSize = oldFile.length();
                        long diff = newSize - oldSize;

                        // 显示详细的差异信息
                        log("   大小对比: " + formatFileSize(oldSize) + " -> " + formatFileSize(newSize));
                        if (diff > 0) {
                            log("   增大: +" + formatFileSize(diff));
                        } else if (diff < 0) {
                            log("   减小: -" + formatFileSize(Math.abs(diff)));
                        }

                        log("   " + result.detailDescription);
                        totalDifferences[0]++;
                    } else if (result.hasFormatChanges) {
                        log("[格式变化] " + currentPath + " - " + result.changeDescription);
                        log("   " + result.detailDescription);
                        // 格式变化不计入需要复制的差异
                    } else {
                        log("[相同] " + currentPath + " - 文件完全相同");
                    }
                } else {
                    // 类型不同
                    String newType = newFile.isDirectory() ? "文件夹" : "文件";
                    String oldType = oldFile.isDirectory() ? "文件夹" : "文件";
                    log("[类型变化] " + currentPath + " - 类型变化 (" + oldType + " -> " + newType + ")");
                    totalDifferences[0]++;
                }
            } else if (newFile.exists()) {
                // 只在新目录存在
                if (newFile.isDirectory()) {
                    log("[新增] " + currentPath + "/ - 新增文件夹 (可能包含新的配置文件)");
                    // 递归显示新增文件夹中的所有文件
                    displayNewDirectoryContents(newFile, currentPath + "/", totalDifferences);
                } else {
                    long size = newFile.length();
                    log("[新增] " + currentPath + " - 新增文件 (" + formatFileSize(size) + ")");
                    if (name.endsWith(".json")) {
                        log("   [提示] 这是新的JSON配置文件，表示启用了新功能");
                    }
                    totalDifferences[0]++;
                }
            } else if (oldFile.exists()) {
                // 只在旧目录存在
                if (oldFile.isDirectory()) {
                    log("[删除] " + currentPath + "/ - 删除文件夹 (可能不再需要相关配置)");
                    // 递归显示被删除文件夹中的所有文件
                    displayDeletedDirectoryContents(oldFile, currentPath + "/", totalDifferences);
                } else {
                    long size = oldFile.length();
                    log("[删除] " + currentPath + " - 删除文件 (原大小: " + formatFileSize(size) + ")");
                    if (name.endsWith(".json")) {
                        log("   [警告] JSON配置文件被删除，可能影响功能");
                    }
                    totalDifferences[0]++;
                }
            }
        }
    }

    // 内容对比结果类
    private static class ContentComparisonResult {
        boolean hasRealChanges;      // 真正的内容变化（需要复制）
        boolean hasFormatChanges;    // 仅格式变化（不需要复制）
        String changeDescription;    // 变化描述
        String detailDescription;    // 详细描述

        ContentComparisonResult(boolean hasRealChanges, boolean hasFormatChanges,
                              String changeDescription, String detailDescription) {
            this.hasRealChanges = hasRealChanges;
            this.hasFormatChanges = hasFormatChanges;
            this.changeDescription = changeDescription;
            this.detailDescription = detailDescription;
        }
    }

    private ContentComparisonResult compareFileContent(File newFile, File oldFile, String fileName) {
        try {
            // 对于JSON文件，进行智能内容对比
            if (fileName.endsWith(".json")) {
                return compareJsonContent(newFile, oldFile);
            }
            // 对于其他文件，进行文本对比
            else if (fileName.endsWith(".properties") || fileName.endsWith(".txt")) {
                return compareTextContent(newFile, oldFile);
            }
            // 对于二进制文件，直接字节对比
            else {
                return compareBinaryContent(newFile, oldFile);
            }
        } catch (Exception e) {
            // 如果对比失败，默认认为有变化
            return new ContentComparisonResult(true, false,
                "文件对比失败", "无法分析文件差异: " + e.getMessage());
        }
    }

    private ContentComparisonResult compareJsonContent(File newFile, File oldFile) {
        try {
            String newContent = Files.readString(newFile.toPath());
            String oldContent = Files.readString(oldFile.toPath());

            // 标准化JSON内容（去除空格、换行、重新排序）
            String normalizedNew = normalizeJsonContent(newContent);
            String normalizedOld = normalizeJsonContent(oldContent);

            if (normalizedNew.equals(normalizedOld)) {
                // 内容相同，只是格式不同
                return new ContentComparisonResult(false, true,
                    "仅格式变化（空格、换行、顺序）",
                    "[建议] 无需复制，JSON配置内容完全相同");
            } else {
                // 内容真的不同
                return analyzeJsonRealChanges(newContent, oldContent);
            }

        } catch (Exception e) {
            return new ContentComparisonResult(true, false,
                "JSON文件对比失败", "无法解析JSON: " + e.getMessage());
        }
    }

    private String normalizeJsonContent(String jsonContent) {
        try {
            // 简单的JSON标准化：去除多余空格和换行
            return jsonContent
                .replaceAll("\\s+", " ")  // 多个空格变成一个
                .replaceAll("\\s*([{}\\[\\],:])", "$1")  // 去除符号前的空格
                .replaceAll("([{}\\[\\],:])\\s*", "$1")  // 去除符号后的空格
                .trim();
        } catch (Exception e) {
            return jsonContent;
        }
    }

    private ContentComparisonResult analyzeJsonRealChanges(String newContent, String oldContent) {
        try {
            // 简单统计JSON中的关键元素数量
            int newClassCount = countOccurrences(newContent, "\"name\"");
            int oldClassCount = countOccurrences(oldContent, "\"name\"");

            int newMethodCount = countOccurrences(newContent, "\"methods\"");
            int oldMethodCount = countOccurrences(oldContent, "\"methods\"");

            int newPatternCount = countOccurrences(newContent, "\"pattern\"");
            int oldPatternCount = countOccurrences(oldContent, "\"pattern\"");

            StringBuilder description = new StringBuilder();
            description.append("[真实变化] ");

            if (newClassCount != oldClassCount) {
                description.append("类配置: ").append(oldClassCount).append(" -> ").append(newClassCount).append(" ");
            }
            if (newMethodCount != oldMethodCount) {
                description.append("方法配置: ").append(oldMethodCount).append(" -> ").append(newMethodCount).append(" ");
            }
            if (newPatternCount != oldPatternCount) {
                description.append("资源模式: ").append(oldPatternCount).append(" -> ").append(newPatternCount).append(" ");
            }

            String detail = "[建议] 需要复制更新，配置内容发生实质性变化";

            return new ContentComparisonResult(true, false,
                "配置内容发生实质性变化", description.toString() + detail);

        } catch (Exception e) {
            return new ContentComparisonResult(true, false,
                "配置内容可能发生变化", "[建议] 建议复制更新，无法确定具体变化");
        }
    }

    private ContentComparisonResult compareTextContent(File newFile, File oldFile) {
        try {
            List<String> newLines = Files.readAllLines(newFile.toPath());
            List<String> oldLines = Files.readAllLines(oldFile.toPath());

            // 去除空行和空格后对比
            List<String> newNormalized = newLines.stream()
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .sorted()
                .collect(java.util.stream.Collectors.toList());

            List<String> oldNormalized = oldLines.stream()
                .map(String::trim)
                .filter(line -> !line.isEmpty())
                .sorted()
                .collect(java.util.stream.Collectors.toList());

            if (newNormalized.equals(oldNormalized)) {
                return new ContentComparisonResult(false, true,
                    "仅格式变化（空行、空格）",
                    "[建议] 无需复制，文本内容完全相同");
            } else {
                return new ContentComparisonResult(true, false,
                    "文本内容发生变化",
                    "[建议] 需要复制更新，文本内容有实质性变化");
            }

        } catch (Exception e) {
            return new ContentComparisonResult(true, false,
                "文本文件对比失败", "无法读取文件: " + e.getMessage());
        }
    }

    private ContentComparisonResult compareBinaryContent(File newFile, File oldFile) {
        try {
            byte[] newBytes = Files.readAllBytes(newFile.toPath());
            byte[] oldBytes = Files.readAllBytes(oldFile.toPath());

            if (Arrays.equals(newBytes, oldBytes)) {
                return new ContentComparisonResult(false, false,
                    "文件完全相同", "");
            } else {
                return new ContentComparisonResult(true, false,
                    "二进制文件发生变化",
                    "[建议] 需要复制更新");
            }

        } catch (Exception e) {
            return new ContentComparisonResult(true, false,
                "二进制文件对比失败", "无法读取文件: " + e.getMessage());
        }
    }

    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }

    private void displayNewDirectoryContents(File dir, String basePath, int[] totalDifferences) {
        File[] files = dir.listFiles();
        if (files == null) return;

        for (File file : files) {
            String currentPath = basePath + file.getName();
            if (file.isDirectory()) {
                log("   [新增文件夹] " + currentPath + "/");
                displayNewDirectoryContents(file, currentPath + "/", totalDifferences);
            } else {
                long size = file.length();
                log("   [新增文件] " + currentPath + " (" + formatFileSize(size) + ")");
                if (file.getName().endsWith(".json")) {
                    log("     [提示] 新的JSON配置文件");
                }
                totalDifferences[0]++;
            }
        }
    }

    private void displayDeletedDirectoryContents(File dir, String basePath, int[] totalDifferences) {
        File[] files = dir.listFiles();
        if (files == null) return;

        for (File file : files) {
            String currentPath = basePath + file.getName();
            if (file.isDirectory()) {
                log("   [删除文件夹] " + currentPath + "/");
                displayDeletedDirectoryContents(file, currentPath + "/", totalDifferences);
            } else {
                long size = file.length();
                log("   [删除文件] " + currentPath + " (原大小: " + formatFileSize(size) + ")");
                if (file.getName().endsWith(".json")) {
                    log("     [警告] JSON配置文件被删除");
                }
                totalDifferences[0]++;
            }
        }
    }

    private void copyMetadata() {
        log("=== 复制元数据文件 ===");

        File newDir = new File(newMetadataDir);
        if (!newDir.exists()) {
            log("❌ 源目录不存在，无法复制");
            return;
        }

        File[] allFiles = newDir.listFiles();
        if (allFiles == null || allFiles.length == 0) {
            log("❌ 源目录为空，没有内容可复制");
            return;
        }

        // 统计要复制的内容
        int fileCount = 0;
        int dirCount = 0;
        for (File file : allFiles) {
            if (file.isFile()) {
                fileCount++;
            } else if (file.isDirectory()) {
                dirCount++;
            }
        }

        // 确认对话框
        String message = String.format(
            "确定要将所有内容复制到目标目录吗？\n\n" +
            "源目录: %s\n" +
            "目标目录: %s\n\n" +
            "将复制:\n" +
            "• %d 个文件\n" +
            "• %d 个文件夹\n\n" +
            "⚠️ 这将覆盖目标目录中的同名文件和文件夹！",
            newMetadataDir, targetDir, fileCount, dirCount
        );

        int result = JOptionPane.showConfirmDialog(
            this,
            message,
            "确认复制",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            log("❌ 用户取消了复制操作");
            return;
        }

        try {
            File targetDirFile = new File(targetDir);
            if (!targetDirFile.exists()) {
                targetDirFile.mkdirs();
                log("📁 创建目标目录: " + targetDir);
            }

            int copiedFiles = 0;
            int copiedDirs = 0;

            for (File file : allFiles) {
                if (file.isFile()) {
                    Path source = file.toPath();
                    Path target = Paths.get(targetDir, file.getName());

                    Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
                    log("📄 复制文件: " + file.getName());
                    copiedFiles++;

                } else if (file.isDirectory()) {
                    Path source = file.toPath();
                    Path target = Paths.get(targetDir, file.getName());

                    copyDirectoryRecursively(source, target);
                    log("📁 复制文件夹: " + file.getName() + "/");
                    copiedDirs++;
                }
            }

            log("✅ 复制完成!");
            log("   📄 文件: " + copiedFiles + " 个");
            log("   📁 文件夹: " + copiedDirs + " 个");

            // 刷新显示
            SwingUtilities.invokeLater(this::refreshStatus);

        } catch (IOException e) {
            log("❌ 复制失败: " + e.getMessage());
            JOptionPane.showMessageDialog(
                this,
                "复制失败: " + e.getMessage(),
                "错误",
                JOptionPane.ERROR_MESSAGE
            );
        }
        log("");
    }

    // 工具方法
    private void copyDirectoryRecursively(Path source, Path target) throws IOException {
        if (!Files.exists(target)) {
            Files.createDirectories(target);
        }

        try (var stream = Files.walk(source)) {
            stream.forEach(sourcePath -> {
                try {
                    Path targetPath = target.resolve(source.relativize(sourcePath));
                    if (Files.isDirectory(sourcePath)) {
                        if (!Files.exists(targetPath)) {
                            Files.createDirectories(targetPath);
                        }
                    } else {
                        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                    }
                } catch (IOException e) {
                    throw new RuntimeException("复制失败: " + sourcePath, e);
                }
            });
        }
    }

    private boolean isValidJson(File file) {
        try {
            String content = Files.readString(file.toPath());
            content = content.trim();
            return (content.startsWith("{") && content.endsWith("}")) ||
                   (content.startsWith("[") && content.endsWith("]"));
        } catch (IOException e) {
            return false;
        }
    }



    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }

    private void initializeLogStyles() {
        // 普通文本样式
        normalStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(normalStyle, Color.BLACK);

        // 成功样式 - 绿色
        successStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(successStyle, new Color(0, 128, 0));
        StyleConstants.setBold(successStyle, true);

        // 错误样式 - 红色
        errorStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(errorStyle, new Color(220, 20, 60));
        StyleConstants.setBold(errorStyle, true);

        // 警告样式 - 橙色
        warningStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(warningStyle, new Color(255, 140, 0));
        StyleConstants.setBold(warningStyle, true);

        // 信息样式 - 蓝色
        infoStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(infoStyle, new Color(30, 144, 255));

        // 文件变化样式 - 紫色
        changeStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(changeStyle, new Color(138, 43, 226));
        StyleConstants.setBold(changeStyle, true);

        // 新增内容样式 - 深绿色
        addStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(addStyle, new Color(34, 139, 34));
        StyleConstants.setBold(addStyle, true);

        // 删除内容样式 - 深红色
        deleteStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(deleteStyle, new Color(178, 34, 34));
        StyleConstants.setBold(deleteStyle, true);

        // 大小变化样式 - 橙红色
        sizeStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(sizeStyle, new Color(255, 69, 0));
        StyleConstants.setBold(sizeStyle, true);

        // 位置信息样式 - 深蓝色
        locationStyle = new SimpleAttributeSet();
        StyleConstants.setForeground(locationStyle, new Color(25, 25, 112));
        StyleConstants.setBold(locationStyle, true);
    }

    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            try {
                SimpleAttributeSet style = getStyleForMessage(message);
                logDocument.insertString(logDocument.getLength(), message + "\n", style);
                logArea.setCaretPosition(logDocument.getLength());
            } catch (BadLocationException e) {
                // 如果插入失败，使用普通方式
                System.out.println(message);
            }
        });
    }

    private SimpleAttributeSet getStyleForMessage(String message) {
        // 成功相关
        if (message.contains("[成功]") || message.contains("[正常]") || message.contains("[相同]")) {
            return successStyle;
        }
        // 错误相关
        else if (message.contains("[错误]") || message.contains("错误:") || message.contains("失败")) {
            return errorStyle;
        }
        // 警告相关
        else if (message.contains("[警告]") || message.contains("警告:")) {
            return warningStyle;
        }
        // 信息相关
        else if (message.contains("[信息]") || message.contains("信息:")) {
            return infoStyle;
        }
        // 新增内容 - 深绿色
        else if (message.contains("[新增]") || message.contains("新增了") || message.contains("新增文件") || message.contains("新增文件夹")) {
            return addStyle;
        }
        // 删除内容 - 深红色
        else if (message.contains("[删除]") || message.contains("减少了") || message.contains("删除文件") || message.contains("删除文件夹")) {
            return deleteStyle;
        }
        // 大小变化 - 橙红色
        else if (message.contains("大小对比") || message.contains("增大:") || message.contains("减小:") ||
                 message.contains("文件增大") || message.contains("文件减小")) {
            return sizeStyle;
        }
        // 位置和差异信息 - 深蓝色
        else if (message.contains("差异位置") || message.contains("首个差异") || message.contains("旧内容:") ||
                 message.contains("新内容:") || message.contains("行数变化")) {
            return locationStyle;
        }
        // 文件变化 - 紫色
        else if (message.contains("[变化]") || message.contains("文件有变化") || message.contains("类型变化")) {
            return changeStyle;
        }
        // 默认样式
        else {
            return normalStyle;
        }
    }

    public static void main(String[] args) {
        // 设置系统属性确保正确的字符编码
        System.setProperty("file.encoding", "UTF-8");
        System.setProperty("sun.jnu.encoding", "UTF-8");

        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());

                // 设置字体以支持中文显示
                Font defaultFont = new Font("Microsoft YaHei", Font.PLAIN, 12);
                Font monoFont = new Font("Consolas", Font.PLAIN, 11);

                UIManager.put("Label.font", defaultFont);
                UIManager.put("Button.font", defaultFont);
                UIManager.put("TextArea.font", monoFont);
                UIManager.put("TextField.font", defaultFont);
                UIManager.put("TitledBorder.font", defaultFont.deriveFont(Font.BOLD));

            } catch (Exception e) {
                // 使用默认外观
            }

            new MetadataChecker().setVisible(true);
        });
    }
}
