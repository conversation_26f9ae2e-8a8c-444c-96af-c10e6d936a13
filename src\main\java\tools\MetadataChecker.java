package tools;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.security.MessageDigest;
import java.util.*;

/**
 * GraalVM 元数据检查和管理工具
 * 用于检查、对比和复制GraalVM Native Image元数据文件
 */
public class MetadataChecker extends JFrame {
    
    private static final String NEW_METADATA_DIR = "metadata/META-INF/native-image";
    private static final String OLD_METADATA_DIR = "src/main/resources/META-INF/native-image";
    
    private JTextArea logArea;
    private JButton checkButton;
    private JButton compareButton;
    private JButton copyButton;
    private JButton refreshButton;
    
    private JPanel newMetadataPanel;
    private JPanel oldMetadataPanel;
    
    public MetadataChecker() {
        initializeGUI();
        refreshStatus();
    }
    
    private void initializeGUI() {
        setTitle("GraalVM 元数据管理工具");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 顶部信息面板
        JPanel infoPanel = createInfoPanel();
        mainPanel.add(infoPanel, BorderLayout.NORTH);
        
        // 中间内容面板
        JPanel contentPanel = new JPanel(new GridLayout(1, 2, 10, 0));
        
        // 新元数据面板
        newMetadataPanel = createMetadataPanel("新收集的元数据", NEW_METADATA_DIR);
        contentPanel.add(newMetadataPanel);
        
        // 旧元数据面板
        oldMetadataPanel = createMetadataPanel("Resources中的元数据", OLD_METADATA_DIR);
        contentPanel.add(oldMetadataPanel);
        
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        
        // 底部按钮面板
        JPanel buttonPanel = createButtonPanel();
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        // 日志面板
        JPanel logPanel = createLogPanel();
        
        // 使用分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT, mainPanel, logPanel);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.7);
        
        add(splitPane);
        
        // 设置窗口属性
        setSize(900, 700);
        setLocationRelativeTo(null);
    }
    
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createTitledBorder("使用说明"));
        
        JLabel infoLabel = new JLabel("<html>" +
            "1. 使用JVM参数收集元数据: -agentlib:native-image-agent=config-merge-dir=metadata/META-INF/native-image<br>" +
            "2. 点击'刷新状态'查看收集到的元数据<br>" +
            "3. 点击'检查完整性'验证JSON文件格式<br>" +
            "4. 点击'对比差异'查看新旧元数据的区别<br>" +
            "5. 点击'复制到Resources'更新构建用的元数据" +
            "</html>");
        
        panel.add(infoLabel);
        return panel;
    }
    
    private JPanel createMetadataPanel(String title, String directory) {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(title));
        
        JTextArea textArea = new JTextArea(15, 30);
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        
        JScrollPane scrollPane = new JScrollPane(textArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        refreshButton = new JButton("刷新状态");
        checkButton = new JButton("检查完整性");
        compareButton = new JButton("对比差异");
        copyButton = new JButton("复制到Resources");
        
        // 添加按钮事件
        refreshButton.addActionListener(e -> refreshStatus());
        checkButton.addActionListener(e -> checkIntegrity());
        compareButton.addActionListener(e -> compareMetadata());
        copyButton.addActionListener(e -> copyMetadata());
        
        panel.add(refreshButton);
        panel.add(checkButton);
        panel.add(compareButton);
        panel.add(copyButton);
        
        return panel;
    }
    
    private JPanel createLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("操作日志"));
        
        logArea = new JTextArea(8, 80);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));
        
        JScrollPane scrollPane = new JScrollPane(logArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 清除日志按钮
        JButton clearLogButton = new JButton("清除日志");
        clearLogButton.addActionListener(e -> logArea.setText(""));
        
        JPanel logButtonPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        logButtonPanel.add(clearLogButton);
        panel.add(logButtonPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    private void refreshStatus() {
        log("=== 刷新元数据状态 ===");
        
        // 更新新元数据面板
        updateMetadataPanel(newMetadataPanel, NEW_METADATA_DIR, "新收集的元数据");
        
        // 更新旧元数据面板
        updateMetadataPanel(oldMetadataPanel, OLD_METADATA_DIR, "Resources中的元数据");
        
        log("状态刷新完成\n");
    }
    
    private void updateMetadataPanel(JPanel panel, String directory, String title) {
        JTextArea textArea = getTextAreaFromPanel(panel);
        StringBuilder content = new StringBuilder();
        
        File dir = new File(directory);
        if (!dir.exists()) {
            content.append("目录不存在: ").append(directory).append("\n\n");
            if (directory.equals(NEW_METADATA_DIR)) {
                content.append("请先运行程序收集元数据\n");
                content.append("JVM参数:\n");
                content.append("-agentlib:native-image-agent=\n");
                content.append("config-merge-dir=metadata/META-INF/native-image");
            } else {
                content.append("这是首次收集元数据");
            }
        } else {
            File[] jsonFiles = dir.listFiles((d, name) -> name.endsWith(".json"));
            if (jsonFiles == null || jsonFiles.length == 0) {
                content.append("目录存在但没有JSON文件\n");
            } else {
                content.append("找到 ").append(jsonFiles.length).append(" 个JSON文件:\n\n");
                
                long totalSize = 0;
                for (File file : jsonFiles) {
                    long size = file.length();
                    totalSize += size;
                    content.append(String.format("📄 %s\n", file.getName()));
                    content.append(String.format("   大小: %s\n", formatFileSize(size)));
                    content.append(String.format("   修改: %s\n\n", 
                        new Date(file.lastModified()).toString()));
                }
                
                content.append("总大小: ").append(formatFileSize(totalSize));
            }
        }
        
        textArea.setText(content.toString());
    }
    
    private JTextArea getTextAreaFromPanel(JPanel panel) {
        Component[] components = panel.getComponents();
        for (Component comp : components) {
            if (comp instanceof JScrollPane) {
                JScrollPane scrollPane = (JScrollPane) comp;
                return (JTextArea) scrollPane.getViewport().getView();
            }
        }
        return null;
    }
    
    private void checkIntegrity() {
        log("=== 检查元数据完整性 ===");
        
        File newDir = new File(NEW_METADATA_DIR);
        if (!newDir.exists()) {
            log("❌ 新元数据目录不存在，请先收集元数据");
            return;
        }
        
        String[] requiredFiles = {
            "reflect-config.json",
            "resource-config.json", 
            "proxy-config.json",
            "jni-config.json",
            "serialization-config.json"
        };
        
        int issues = 0;
        for (String fileName : requiredFiles) {
            File file = new File(newDir, fileName);
            if (file.exists()) {
                if (isValidJson(file)) {
                    long size = file.length();
                    if (size < 10) {
                        log("⚠️  " + fileName + " - 文件很小 (" + size + " bytes)");
                    } else {
                        log("✅ " + fileName + " - 格式正确 (" + formatFileSize(size) + ")");
                    }
                } else {
                    log("❌ " + fileName + " - JSON格式错误!");
                    issues++;
                }
            } else {
                log("❓ " + fileName + " - 文件不存在 (可能未使用相关功能)");
            }
        }
        
        if (issues == 0) {
            log("✅ 元数据完整性检查通过!");
        } else {
            log("❌ 发现 " + issues + " 个问题!");
        }
        log("");
    }

    private void compareMetadata() {
        log("=== 对比元数据差异 ===");

        File newDir = new File(NEW_METADATA_DIR);
        File oldDir = new File(OLD_METADATA_DIR);

        if (!newDir.exists()) {
            log("❌ 新元数据目录不存在");
            return;
        }

        if (!oldDir.exists()) {
            log("ℹ️  Resources目录中没有元数据，这是首次收集");
            log("");
            return;
        }

        String[] configFiles = {
            "reflect-config.json",
            "resource-config.json",
            "proxy-config.json",
            "jni-config.json",
            "serialization-config.json"
        };

        int differences = 0;
        for (String fileName : configFiles) {
            File newFile = new File(newDir, fileName);
            File oldFile = new File(oldDir, fileName);

            if (newFile.exists()) {
                if (oldFile.exists()) {
                    if (!filesEqual(newFile, oldFile)) {
                        log("🔄 " + fileName + " - 文件有变化");
                        long newSize = newFile.length();
                        long oldSize = oldFile.length();
                        long diff = newSize - oldSize;
                        if (diff > 0) {
                            log("   📈 文件增大了 " + diff + " bytes");
                        } else if (diff < 0) {
                            log("   📉 文件减小了 " + Math.abs(diff) + " bytes");
                        } else {
                            log("   🔄 大小相同但内容不同");
                        }
                        differences++;
                    } else {
                        log("✅ " + fileName + " - 文件相同");
                    }
                } else {
                    log("🆕 " + fileName + " - 新增文件");
                    differences++;
                }
            } else {
                if (oldFile.exists()) {
                    log("🗑️  " + fileName + " - 文件已删除");
                    differences++;
                }
            }
        }

        if (differences == 0) {
            log("ℹ️  所有文件都相同，无需更新");
        } else {
            log("ℹ️  发现 " + differences + " 个文件有差异");
        }
        log("");
    }

    private void copyMetadata() {
        log("=== 复制元数据到Resources ===");

        File newDir = new File(NEW_METADATA_DIR);
        if (!newDir.exists()) {
            log("❌ 新元数据目录不存在，无法复制");
            return;
        }

        File[] jsonFiles = newDir.listFiles((d, name) -> name.endsWith(".json"));
        if (jsonFiles == null || jsonFiles.length == 0) {
            log("❌ 没有找到JSON文件");
            return;
        }

        // 确认对话框
        int result = JOptionPane.showConfirmDialog(
            this,
            "确定要将新元数据复制到Resources目录吗？\n这将覆盖现有的元数据文件。",
            "确认复制",
            JOptionPane.YES_NO_OPTION,
            JOptionPane.QUESTION_MESSAGE
        );

        if (result != JOptionPane.YES_OPTION) {
            log("❌ 用户取消了复制操作");
            return;
        }

        try {
            File oldDir = new File(OLD_METADATA_DIR);
            if (!oldDir.exists()) {
                oldDir.mkdirs();
                log("📁 创建目录: " + OLD_METADATA_DIR);
            }

            int copiedCount = 0;
            for (File file : jsonFiles) {
                Path source = file.toPath();
                Path target = Paths.get(OLD_METADATA_DIR, file.getName());

                Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
                log("📋 复制: " + file.getName());
                copiedCount++;
            }

            log("✅ 成功复制 " + copiedCount + " 个文件到Resources目录");

            // 刷新显示
            SwingUtilities.invokeLater(this::refreshStatus);

        } catch (IOException e) {
            log("❌ 复制失败: " + e.getMessage());
            JOptionPane.showMessageDialog(
                this,
                "复制失败: " + e.getMessage(),
                "错误",
                JOptionPane.ERROR_MESSAGE
            );
        }
        log("");
    }

    // 工具方法
    private boolean isValidJson(File file) {
        try {
            String content = Files.readString(file.toPath());
            content = content.trim();
            return (content.startsWith("{") && content.endsWith("}")) ||
                   (content.startsWith("[") && content.endsWith("]"));
        } catch (IOException e) {
            return false;
        }
    }

    private boolean filesEqual(File file1, File file2) {
        try {
            byte[] hash1 = getFileHash(file1);
            byte[] hash2 = getFileHash(file2);
            return Arrays.equals(hash1, hash2);
        } catch (Exception e) {
            return false;
        }
    }

    private byte[] getFileHash(File file) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] fileBytes = Files.readAllBytes(file.toPath());
        return md.digest(fileBytes);
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }

    private void log(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append(message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception e) {
                // 使用默认外观
            }

            new MetadataChecker().setVisible(true);
        });
    }
}
